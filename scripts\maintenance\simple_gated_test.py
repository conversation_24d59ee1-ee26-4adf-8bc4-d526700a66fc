#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script to verify that gated download form submission recording is working correctly
"""

import os
import sqlite3
import json
from datetime import datetime

# Database path
DB_PATH = os.getenv("DB_PATH", "./erdb_main.db")

def test_form_submission_creation():
    """Test that form submissions can be created directly in the database."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🧪 TESTING FORM SUBMISSION CREATION")
        print("=" * 50)
        
        # Get a gated PDF to test with
        cursor.execute("""
            SELECT id, filename, original_filename, category, form_id 
            FROM pdf_documents 
            WHERE form_id IS NOT NULL 
            LIMIT 1
        """)
        
        pdf_record = cursor.fetchone()
        if not pdf_record:
            print("❌ No gated PDFs found for testing")
            return False
            
        pdf_id, filename, original_filename, category, form_id = pdf_record
        print(f"✅ Found test PDF: {original_filename} (ID: {pdf_id}, Category: {category})")
        
        # Get the form details
        cursor.execute("SELECT id, name FROM forms WHERE id = ?", (form_id,))
        form_record = cursor.fetchone()
        if not form_record:
            print("❌ Form not found")
            return False
            
        form_id, form_name = form_record
        print(f"✅ Found form: {form_name} (ID: {form_id})")
        
        # Test submission data
        test_submission_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'organization': 'Test Organization',
            'purpose': 'Testing the gated download fix'
        }
        
        # Create a test submission directly in the database
        print(f"\n🔍 Testing form submission creation...")
        cursor.execute('''
            INSERT INTO form_submissions (form_id, pdf_document_id, submission_data, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ''', (form_id, pdf_id, json.dumps(test_submission_data), '127.0.0.1', 'Test Agent'))
        
        submission_id = cursor.lastrowid
        conn.commit()
        
        if submission_id:
            print(f"✅ Successfully created form submission with ID: {submission_id}")
            
            # Verify the submission was created
            cursor.execute("""
                SELECT fs.id, fs.form_id, fs.pdf_document_id, fs.submission_data, 
                       fs.ip_address, fs.user_agent, fs.submitted_at,
                       f.name as form_name, pd.original_filename as pdf_filename
                FROM form_submissions fs
                JOIN forms f ON fs.form_id = f.id
                JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
                WHERE fs.id = ?
            """, (submission_id,))
            
            submission_record = cursor.fetchone()
            if submission_record:
                sub_id, sub_form_id, sub_pdf_id, sub_data, sub_ip, sub_agent, sub_time, sub_form_name, sub_pdf_name = submission_record
                print(f"✅ Verified submission:")
                print(f"   - Submission ID: {sub_id}")
                print(f"   - Form: {sub_form_name}")
                print(f"   - PDF: {sub_pdf_name}")
                print(f"   - IP: {sub_ip}")
                print(f"   - Submitted: {sub_time}")
                print(f"   - Data: {json.loads(sub_data)}")
                
                # Clean up test submission
                cursor.execute("DELETE FROM form_submissions WHERE id = ?", (submission_id,))
                conn.commit()
                print(f"✅ Cleaned up test submission")
                
                return True
            else:
                print("❌ Could not verify submission was created")
                return False
        else:
            print("❌ Failed to create form submission")
            return False
            
    except Exception as e:
        print(f"❌ Error testing form submission creation: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_fallback_document_handling():
    """Test handling of fallback documents by creating a temporary PDF record."""
    try:
        print(f"\n🧪 TESTING FALLBACK DOCUMENT HANDLING")
        print("=" * 50)
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Test submission data
        test_submission_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'organization': 'Test Organization'
        }
        
        # Test creating a submission for a non-existent PDF
        test_filename = "test_fallback_document.pdf"
        test_category = "MANUAL"
        test_form_id = 1
        
        print(f"🔍 Testing fallback document: {test_filename}")
        
        # First, create a temporary PDF record
        cursor.execute('''
            INSERT INTO pdf_documents (filename, original_filename, category, form_id, upload_date, created_at, updated_at)
            VALUES (?, ?, ?, ?, datetime('now'), datetime('now'), datetime('now'))
        ''', (test_filename, test_filename, test_category, test_form_id))
        
        temp_pdf_id = cursor.lastrowid
        conn.commit()
        
        if temp_pdf_id:
            print(f"✅ Created temporary PDF record with ID: {temp_pdf_id}")
            
            # Now create a form submission for this temporary PDF
            cursor.execute('''
                INSERT INTO form_submissions (form_id, pdf_document_id, submission_data, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            ''', (test_form_id, temp_pdf_id, json.dumps(test_submission_data), '127.0.0.1', 'Test Agent'))
            
            submission_id = cursor.lastrowid
            conn.commit()
            
            if submission_id:
                print(f"✅ Successfully created submission for fallback document with ID: {submission_id}")
                
                # Clean up test data
                cursor.execute("DELETE FROM form_submissions WHERE pdf_document_id = ?", (temp_pdf_id,))
                cursor.execute("DELETE FROM pdf_documents WHERE id = ?", (temp_pdf_id,))
                conn.commit()
                print(f"✅ Cleaned up test data")
                
                return True
            else:
                print("❌ Failed to create submission for fallback document")
                return False
        else:
            print("❌ Failed to create temporary PDF record")
            return False
            
    except Exception as e:
        print(f"❌ Error testing fallback document handling: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """Run all tests."""
    print("🚀 GATED DOWNLOAD FIX VERIFICATION")
    print("=" * 60)
    print(f"Database: {DB_PATH}")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Form Submission Creation", test_form_submission_creation),
        ("Fallback Document Handling", test_fallback_document_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Error in {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The gated download form submission recording is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")

if __name__ == "__main__":
    main() 