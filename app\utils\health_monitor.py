"""
System Health Monitoring for ERDB Document Management System

This module provides comprehensive system health monitoring including:
- Database performance metrics
- Disk usage monitoring
- Memory usage tracking
- Application performance metrics
- System resource alerts
"""

import os
import psutil
import sqlite3
import logging
import datetime
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

# Import database connection with fallback
try:
    from app.utils.db_connection import get_connection
    HAS_DB_CONNECTION = True
except ImportError:
    HAS_DB_CONNECTION = False
    def get_connection(*args, **kwargs):
        return None

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    active_connections: int
    database_size_mb: float
    uptime_seconds: int

@dataclass
class DatabaseMetrics:
    """Database performance metrics."""
    database_name: str
    size_mb: float
    page_count: int
    cache_hit_ratio: float
    active_connections: int
    last_vacuum: Optional[str]
    integrity_check: bool
    last_modified: Optional[str] = None
    file_path: Optional[str] = None
    database_type: str = "SQLite"

@dataclass
class HealthStatus:
    """Overall system health status."""
    status: str  # 'healthy', 'warning', 'critical'
    score: int  # 0-100
    issues: List[str]
    recommendations: List[str]
    last_check: str

class HealthMonitor:
    """System health monitoring and alerting."""
    
    def __init__(self):
        self.thresholds = {
            'cpu_warning': 70.0,
            'cpu_critical': 90.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'db_size_warning': 1000.0,  # 1GB
            'db_size_critical': 5000.0,  # 5GB
            'cache_hit_ratio_warning': 0.8,
            'cache_hit_ratio_critical': 0.6
        }
        
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1000
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Database metrics
            db_size = self._get_database_size()
            active_connections = self._get_active_connections()
            
            # Uptime
            uptime = psutil.boot_time()
            uptime_seconds = int(datetime.datetime.now().timestamp() - uptime)
            
            metrics = SystemMetrics(
                timestamp=datetime.datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available_mb=memory.available / (1024 * 1024),
                disk_usage_percent=disk.percent,
                disk_free_gb=disk.free / (1024 * 1024 * 1024),
                active_connections=active_connections,
                database_size_mb=db_size,
                uptime_seconds=uptime_seconds
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)
            
            return metrics
        
        except Exception as e:
            logger.error(f"Error getting system metrics: {str(e)}")
            raise
    
    def get_database_metrics(self) -> List[DatabaseMetrics]:
        """Get database performance metrics for all databases."""
        # Comprehensive list of all databases used by the application
        databases = [
            "./erdb_main.db",  # Main unified database
            "./user_management.db",  # Legacy user management
            "./chat_history.db",  # Legacy chat history
            "./content_db.sqlite",  # Legacy content database
            "./scraped_pages.db",  # Legacy scraped pages
        ]

        # Add ChromaDB SQLite files
        chroma_databases = self._find_chroma_databases()
        databases.extend(chroma_databases)

        metrics = []
        for db_path in databases:
            if os.path.exists(db_path):
                try:
                    # Get file modification time
                    import datetime
                    mod_time = os.path.getmtime(db_path)
                    last_modified = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')

                    # Determine database type
                    db_type = "ChromaDB" if "chroma.sqlite3" in db_path else "SQLite"

                    # Use direct sqlite3 connection for metrics
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    conn.row_factory = sqlite3.Row

                    try:
                        cursor = conn.cursor()

                        # Get database size
                        cursor.execute("PRAGMA page_count")
                        page_count = cursor.fetchone()[0]
                        cursor.execute("PRAGMA page_size")
                        page_size = cursor.fetchone()[0]
                        size_mb = (page_count * page_size) / (1024 * 1024)

                        # Get cache hit ratio (with error handling for ChromaDB)
                        cache_hit_ratio = 1.0  # Default
                        try:
                            cursor.execute("PRAGMA cache_hit")
                            cache_hit = cursor.fetchone()[0]
                            cursor.execute("PRAGMA cache_miss")
                            cache_miss = cursor.fetchone()[0]

                            total_requests = cache_hit + cache_miss
                            cache_hit_ratio = cache_hit / total_requests if total_requests > 0 else 1.0
                        except:
                            # ChromaDB might not support these pragmas
                            pass

                        # Check integrity
                        integrity_ok = True  # Default
                        try:
                            cursor.execute("PRAGMA integrity_check")
                            integrity_result = cursor.fetchone()[0]
                            integrity_ok = integrity_result == 'ok'
                        except:
                            # Some databases might not support integrity check
                            pass

                        # Get last vacuum info (if available)
                        last_vacuum = None
                        try:
                            cursor.execute("SELECT last_vacuum FROM sqlite_stat1 LIMIT 1")
                            result = cursor.fetchone()
                            if result:
                                last_vacuum = result[0]
                        except:
                            pass

                        metrics.append(DatabaseMetrics(
                            database_name=os.path.basename(db_path),
                            size_mb=size_mb,
                            page_count=page_count,
                            cache_hit_ratio=cache_hit_ratio,
                            active_connections=1,  # Simplified for now
                            last_vacuum=last_vacuum,
                            integrity_check=integrity_ok,
                            last_modified=last_modified,
                            file_path=db_path,
                            database_type=db_type
                        ))

                    finally:
                        conn.close()

                except Exception as e:
                    logger.error(f"Error getting metrics for {db_path}: {str(e)}")
                    # Add a basic entry for databases that can't be analyzed
                    try:
                        size_mb = os.path.getsize(db_path) / (1024 * 1024)
                        mod_time = os.path.getmtime(db_path)
                        last_modified = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                        db_type = "ChromaDB" if "chroma.sqlite3" in db_path else "SQLite"

                        metrics.append(DatabaseMetrics(
                            database_name=os.path.basename(db_path),
                            size_mb=size_mb,
                            page_count=0,
                            cache_hit_ratio=0.0,
                            active_connections=0,
                            last_vacuum=None,
                            integrity_check=False,
                            last_modified=last_modified,
                            file_path=db_path,
                            database_type=db_type
                        ))
                    except Exception as file_error:
                        logger.error(f"Could not get basic file info for {db_path}: {str(file_error)}")
        
        return metrics

    def _find_chroma_databases(self) -> List[str]:
        """Find all ChromaDB SQLite files in the system."""
        chroma_databases = []

        # Search in common ChromaDB locations
        search_paths = [
            "./data/unified_chroma",
            "./data/chroma",
            "./data/chroma/CANOPY",
            "./data/chroma/MANUAL",
            "./data/chroma/RISE"
        ]

        for search_path in search_paths:
            if os.path.exists(search_path):
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file == "chroma.sqlite3":
                            full_path = os.path.join(root, file)
                            chroma_databases.append(full_path)

        return chroma_databases

    def vacuum_all_databases(self) -> Dict[str, Any]:
        """Perform VACUUM operation on all databases."""
        results = {
            'success': True,
            'total_databases': 0,
            'successful_vacuums': 0,
            'failed_vacuums': 0,
            'total_space_reclaimed_mb': 0.0,
            'details': [],
            'errors': []
        }

        # Get all databases
        databases = [
            "./erdb_main.db",
            "./user_management.db",
            "./chat_history.db",
            "./content_db.sqlite",
            "./scraped_pages.db",
        ]

        # Add ChromaDB files
        chroma_databases = self._find_chroma_databases()
        databases.extend(chroma_databases)

        results['total_databases'] = len([db for db in databases if os.path.exists(db)])

        for db_path in databases:
            if not os.path.exists(db_path):
                continue

            try:
                # Get size before vacuum
                size_before = os.path.getsize(db_path) / (1024 * 1024)  # MB

                # Perform vacuum operation
                vacuum_result = self._vacuum_single_database(db_path)

                if vacuum_result['success']:
                    results['successful_vacuums'] += 1
                    space_reclaimed = vacuum_result.get('space_reclaimed_mb', 0)
                    results['total_space_reclaimed_mb'] += space_reclaimed

                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'success': True,
                        'size_before_mb': size_before,
                        'size_after_mb': vacuum_result.get('size_after_mb', size_before),
                        'space_reclaimed_mb': space_reclaimed,
                        'duration_seconds': vacuum_result.get('duration_seconds', 0)
                    })
                else:
                    results['failed_vacuums'] += 1
                    results['errors'].append(f"{os.path.basename(db_path)}: {vacuum_result.get('error', 'Unknown error')}")
                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'success': False,
                        'error': vacuum_result.get('error', 'Unknown error')
                    })

            except Exception as e:
                results['failed_vacuums'] += 1
                error_msg = str(e)
                results['errors'].append(f"{os.path.basename(db_path)}: {error_msg}")
                results['details'].append({
                    'database': os.path.basename(db_path),
                    'path': db_path,
                    'success': False,
                    'error': error_msg
                })
                logger.error(f"Error vacuuming {db_path}: {error_msg}")

        # Determine overall success
        if results['failed_vacuums'] > 0:
            results['success'] = results['successful_vacuums'] > 0  # Partial success if some succeeded

        return results

    def _vacuum_single_database(self, db_path: str) -> Dict[str, Any]:
        """Perform VACUUM operation on a single database."""
        import time

        result = {
            'success': False,
            'error': None,
            'duration_seconds': 0,
            'size_before_mb': 0,
            'size_after_mb': 0,
            'space_reclaimed_mb': 0
        }

        try:
            start_time = time.time()

            # Check if file exists and is accessible
            if not os.path.exists(db_path):
                result['error'] = f"Database file not found: {db_path}"
                return result

            if not os.access(db_path, os.R_OK | os.W_OK):
                result['error'] = f"Insufficient permissions to access database: {db_path}"
                return result

            # Get size before
            size_before = os.path.getsize(db_path)
            result['size_before_mb'] = size_before / (1024 * 1024)

            # Special handling for ChromaDB
            if "chroma.sqlite3" in db_path:
                # Use the existing vacuum_chromadb function if available
                try:
                    from app.utils.helpers import vacuum_chromadb
                    success, size_before_helper, size_after_helper, space_reclaimed = vacuum_chromadb()

                    if success:
                        result['success'] = True
                        result['size_after_mb'] = size_after_helper
                        result['space_reclaimed_mb'] = space_reclaimed
                    else:
                        result['error'] = "ChromaDB vacuum failed"

                except ImportError:
                    # Fall back to standard SQLite vacuum
                    result = self._standard_sqlite_vacuum(db_path, result, start_time)
            else:
                # Standard SQLite vacuum
                result = self._standard_sqlite_vacuum(db_path, result, start_time)

            result['duration_seconds'] = time.time() - start_time

        except Exception as e:
            result['error'] = str(e)
            result['duration_seconds'] = time.time() - start_time if 'start_time' in locals() else 0

        return result

    def _standard_sqlite_vacuum(self, db_path: str, result: Dict[str, Any], start_time: float) -> Dict[str, Any]:
        """Perform standard SQLite VACUUM operation."""
        try:
            # Use direct sqlite3 connection for vacuum operations
            import sqlite3
            conn = sqlite3.connect(db_path)
            conn.row_factory = sqlite3.Row

            try:
                cursor = conn.cursor()

                # Set a reasonable timeout for the vacuum operation
                cursor.execute("PRAGMA busy_timeout = 30000")  # 30 seconds

                # Get page info before vacuum
                cursor.execute("PRAGMA page_count")
                page_count_before = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]

                # Perform VACUUM with error handling for common issues
                try:
                    cursor.execute("VACUUM")
                except Exception as vacuum_error:
                    error_msg = str(vacuum_error).lower()
                    if 'database is locked' in error_msg:
                        result['error'] = "Database is currently locked by another process. Please try again later."
                    elif 'disk' in error_msg and 'full' in error_msg:
                        result['error'] = "Insufficient disk space to complete vacuum operation."
                    elif 'permission' in error_msg:
                        result['error'] = "Permission denied. Cannot write to database file."
                    else:
                        result['error'] = f"Vacuum operation failed: {str(vacuum_error)}"
                    return result

                # Get page info after vacuum
                cursor.execute("PRAGMA page_count")
                page_count_after = cursor.fetchone()[0]

                # Calculate space reclaimed
                size_before = page_count_before * page_size
                size_after = page_count_after * page_size
                space_reclaimed = size_before - size_after

                result['success'] = True
                result['size_after_mb'] = size_after / (1024 * 1024)
                result['space_reclaimed_mb'] = space_reclaimed / (1024 * 1024)

            finally:
                conn.close()

        except Exception as e:
            error_msg = str(e).lower()
            if 'database is locked' in error_msg:
                result['error'] = "Database is currently locked by another process. Please try again later."
            elif 'permission' in error_msg:
                result['error'] = "Permission denied. Cannot access database file."
            else:
                result['error'] = f"Database connection failed: {str(e)}"

        return result

    def check_health_status(self) -> HealthStatus:
        """Check overall system health and return status."""
        try:
            system_metrics = self.get_system_metrics()
            db_metrics = self.get_database_metrics()
            
            issues = []
            recommendations = []
            score = 100
            
            # Check CPU usage
            if system_metrics.cpu_percent > self.thresholds['cpu_critical']:
                issues.append(f"CPU usage critical: {system_metrics.cpu_percent:.1f}%")
                recommendations.append("Consider scaling up CPU resources or optimizing queries")
                score -= 30
            elif system_metrics.cpu_percent > self.thresholds['cpu_warning']:
                issues.append(f"CPU usage high: {system_metrics.cpu_percent:.1f}%")
                recommendations.append("Monitor CPU usage and consider optimization")
                score -= 10
            
            # Check memory usage
            if system_metrics.memory_percent > self.thresholds['memory_critical']:
                issues.append(f"Memory usage critical: {system_metrics.memory_percent:.1f}%")
                recommendations.append("Increase system memory or optimize memory usage")
                score -= 30
            elif system_metrics.memory_percent > self.thresholds['memory_warning']:
                issues.append(f"Memory usage high: {system_metrics.memory_percent:.1f}%")
                recommendations.append("Monitor memory usage and consider cleanup")
                score -= 10
            
            # Check disk usage
            if system_metrics.disk_usage_percent > self.thresholds['disk_critical']:
                issues.append(f"Disk usage critical: {system_metrics.disk_usage_percent:.1f}%")
                recommendations.append("Free up disk space immediately")
                score -= 30
            elif system_metrics.disk_usage_percent > self.thresholds['disk_warning']:
                issues.append(f"Disk usage high: {system_metrics.disk_usage_percent:.1f}%")
                recommendations.append("Consider cleanup of old files and backups")
                score -= 10
            
            # Check database metrics
            for db_metric in db_metrics:
                if db_metric.size_mb > self.thresholds['db_size_critical']:
                    issues.append(f"Database {db_metric.database_name} size critical: {db_metric.size_mb:.1f}MB")
                    recommendations.append(f"Run VACUUM on {db_metric.database_name} and consider archiving old data")
                    score -= 20
                elif db_metric.size_mb > self.thresholds['db_size_warning']:
                    issues.append(f"Database {db_metric.database_name} size large: {db_metric.size_mb:.1f}MB")
                    recommendations.append(f"Consider maintenance on {db_metric.database_name}")
                    score -= 5
                
                if db_metric.cache_hit_ratio < self.thresholds['cache_hit_ratio_critical']:
                    issues.append(f"Database {db_metric.database_name} cache hit ratio critical: {db_metric.cache_hit_ratio:.2f}")
                    recommendations.append(f"Optimize queries and indexes for {db_metric.database_name}")
                    score -= 15
                elif db_metric.cache_hit_ratio < self.thresholds['cache_hit_ratio_warning']:
                    issues.append(f"Database {db_metric.database_name} cache hit ratio low: {db_metric.cache_hit_ratio:.2f}")
                    recommendations.append(f"Consider query optimization for {db_metric.database_name}")
                    score -= 5
                
                if not db_metric.integrity_check:
                    issues.append(f"Database {db_metric.database_name} integrity check failed")
                    recommendations.append(f"Run integrity check and repair {db_metric.database_name}")
                    score -= 25
            
            # Determine status
            if score >= 80:
                status = 'healthy'
            elif score >= 60:
                status = 'warning'
            else:
                status = 'critical'
            
            return HealthStatus(
                status=status,
                score=score,
                issues=issues,
                recommendations=recommendations,
                last_check=system_metrics.timestamp
            )
        
        except Exception as e:
            logger.error(f"Error checking health status: {str(e)}")
            return HealthStatus(
                status='unknown',
                score=0,
                issues=[f"Health check failed: {str(e)}"],
                recommendations=["Check system logs and restart monitoring"],
                last_check=datetime.datetime.now().isoformat()
            )
    
    def _get_database_size(self) -> float:
        """Get total database size in MB."""
        total_size = 0
        databases = [
            "./erdb_main.db"  # Unified database
        ]
        
        for db_path in databases:
            if os.path.exists(db_path):
                total_size += os.path.getsize(db_path)
        
        return total_size / (1024 * 1024)
    
    def _get_active_connections(self) -> int:
        """Get number of active database connections."""
        # This is a simplified implementation
        # In a real system, you'd track actual connection pool usage
        return len(self.metrics_history) if self.metrics_history else 0
    
    def get_metrics_history(self, hours: int = 24) -> List[SystemMetrics]:
        """Get metrics history for the specified number of hours."""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        cutoff_timestamp = cutoff_time.isoformat()
        
        return [
            metric for metric in self.metrics_history
            if metric.timestamp >= cutoff_timestamp
        ]
    
    def export_metrics(self, file_path: str) -> bool:
        """Export metrics to JSON file."""
        try:
            data = {
                'system_metrics': [asdict(m) for m in self.metrics_history],
                'database_metrics': [asdict(m) for m in self.get_database_metrics()],
                'health_status': asdict(self.check_health_status()),
                'export_timestamp': datetime.datetime.now().isoformat()
            }
            
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Metrics exported to {file_path}")
            return True
        
        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return False

    def analyze_database_sizes(self) -> Dict[str, Any]:
        """Analyze all database sizes and return comprehensive statistics."""
        logger.info("Analyzing database sizes...")
        
        databases = [
            "./erdb_main.db",
            "./chat_history.db", 
            "./user_management.db",
            "./scraped_pages.db",
            "./data/content_db.sqlite",
            "./data/erdb.db",
            "./data/unified_chroma/chroma.sqlite3",
            "./data/itisSqlite060625/ITIS.sqlite"
        ]
        
        analysis = {
            'timestamp': datetime.datetime.now().isoformat(),
            'databases': {},
            'summary': {
                'total_size_mb': 0,
                'active_databases': 0,
                'largest_database': None,
                'smallest_database': None
            }
        }
        
        total_size = 0
        active_count = 0
        sizes = []
        
        for db_path in databases:
            if not os.path.exists(db_path):
                continue
                
            try:
                size_bytes = os.path.getsize(db_path)
                size_mb = size_bytes / (1024 * 1024)
                
                db_name = os.path.basename(db_path)
                db_info = {
                    'name': db_name,
                    'path': db_path,
                    'size_mb': size_mb,
                    'size_bytes': size_bytes,
                    'last_modified': datetime.datetime.fromtimestamp(os.path.getmtime(db_path)).isoformat(),
                    'exists': True
                }
                
                # Get SQLite-specific info if applicable
                if self._is_sqlite_database(db_path):
                    sqlite_info = self._analyze_sqlite_database(db_path)
                    db_info.update(sqlite_info)
                
                analysis['databases'][db_name] = db_info
                total_size += size_mb
                active_count += 1
                sizes.append((db_name, size_mb))
                
            except Exception as e:
                logger.error(f"Error analyzing database {db_path}: {str(e)}")
        
        # Calculate summary
        analysis['summary']['total_size_mb'] = total_size
        analysis['summary']['active_databases'] = active_count
        
        if sizes:
            sizes.sort(key=lambda x: x[1], reverse=True)
            analysis['summary']['largest_database'] = {
                'name': sizes[0][0],
                'size_mb': sizes[0][1]
            }
            analysis['summary']['smallest_database'] = {
                'name': sizes[-1][0],
                'size_mb': sizes[-1][1]
            }
        
        return analysis
    
    def _is_sqlite_database(self, path: str) -> bool:
        """Check if a file is a valid SQLite database."""
        try:
            if not os.path.exists(path):
                return False
            
            with open(path, 'rb') as f:
                header = f.read(16)
                return header.startswith(b'SQLite format 3')
        except:
            return False
    
    def _analyze_sqlite_database(self, path: str) -> Dict[str, Any]:
        """Analyze SQLite database structure and statistics."""
        info = {
            'tables': [],
            'row_counts': {},
            'integrity_check': False,
            'vacuum_needed': False,
            'free_pages': 0,
            'total_pages': 0,
            'page_size': 0,
            'journal_mode': 'unknown'
        }
        
        try:
            conn = sqlite3.connect(path)
            cursor = conn.cursor()
            
            # Get database statistics
            cursor.execute("PRAGMA page_count")
            info['total_pages'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            info['page_size'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA freelist_count")
            info['free_pages'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA journal_mode")
            info['journal_mode'] = cursor.fetchone()[0]
            
            # Check if vacuum is needed (more than 10% free pages)
            if info['total_pages'] > 0:
                free_percentage = info['free_pages'] / info['total_pages']
                info['vacuum_needed'] = free_percentage > 0.1
            
            # Get table information
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                info['tables'].append(table_name)
                
                # Get row count for each table
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    info['row_counts'][table_name] = row_count
                except:
                    info['row_counts'][table_name] = 0
            
            # Check integrity
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()
            info['integrity_check'] = integrity_result[0] == 'ok' if integrity_result else False
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error analyzing SQLite database {path}: {str(e)}")
        
        return info
    
    def cleanup_database_space(self) -> Dict[str, Any]:
        """Comprehensive database space cleanup and optimization."""
        logger.info("Starting comprehensive database space cleanup...")
        
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'success': True,
            'databases_processed': 0,
            'total_space_reclaimed_mb': 0,
            'errors': [],
            'details': []
        }
        
        # Get all databases
        databases = [
            "./erdb_main.db",
            "./chat_history.db",
            "./user_management.db", 
            "./scraped_pages.db",
            "./data/content_db.sqlite",
            "./data/erdb.db",
            "./data/unified_chroma/chroma.sqlite3"
        ]
        
        for db_path in databases:
            if not os.path.exists(db_path):
                continue
            
            try:
                logger.info(f"Processing database: {db_path}")
                
                # Get size before cleanup
                size_before = os.path.getsize(db_path) / (1024 * 1024)
                
                # Perform cleanup
                cleanup_result = self._cleanup_single_database(db_path)
                
                if cleanup_result['success']:
                    results['databases_processed'] += 1
                    space_reclaimed = cleanup_result.get('space_reclaimed_mb', 0)
                    results['total_space_reclaimed_mb'] += space_reclaimed
                    
                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'size_before_mb': size_before,
                        'size_after_mb': cleanup_result.get('size_after_mb', size_before),
                        'space_reclaimed_mb': space_reclaimed,
                        'success': True
                    })
                    
                    logger.info(f"Cleaned up {db_path}: reclaimed {space_reclaimed:.2f} MB")
                else:
                    results['errors'].append(f"{os.path.basename(db_path)}: {cleanup_result.get('error', 'Unknown error')}")
                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'success': False,
                        'error': cleanup_result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                error_msg = f"Error processing {db_path}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
                results['success'] = False
        
        logger.info(f"Database cleanup complete. Reclaimed {results['total_space_reclaimed_mb']:.2f} MB total")
        return results
    
    def _cleanup_single_database(self, db_path: str) -> Dict[str, Any]:
        """Clean up a single database."""
        result = {
            'success': False,
            'space_reclaimed_mb': 0,
            'size_after_mb': 0,
            'error': None
        }
        
        try:
            # Get size before
            size_before = os.path.getsize(db_path)
            size_before_mb = size_before / (1024 * 1024)
            
            # Check if it's a SQLite database
            if not self._is_sqlite_database(db_path):
                result['success'] = True
                result['size_after_mb'] = size_before_mb
                return result
            
            # Perform SQLite-specific cleanup
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get initial statistics
            cursor.execute("PRAGMA page_count")
            pages_before = cursor.fetchone()[0]
            cursor.execute("PRAGMA freelist_count")
            free_pages_before = cursor.fetchone()[0]
            
            # Run VACUUM
            logger.info(f"Running VACUUM on {db_path}")
            cursor.execute("VACUUM")
            
            # Run ANALYZE to update statistics
            cursor.execute("ANALYZE")
            
            # Run PRAGMA optimize
            cursor.execute("PRAGMA optimize")
            
            conn.close()
            
            # Get size after
            size_after = os.path.getsize(db_path)
            size_after_mb = size_after / (1024 * 1024)
            space_reclaimed = size_before - size_after
            space_reclaimed_mb = space_reclaimed / (1024 * 1024)
            
            result['success'] = True
            result['space_reclaimed_mb'] = space_reclaimed_mb
            result['size_after_mb'] = size_after_mb
            
            logger.info(f"Cleaned up {db_path}: {size_before_mb:.2f} MB -> {size_after_mb:.2f} MB (reclaimed {space_reclaimed_mb:.2f} MB)")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Error cleaning up {db_path}: {str(e)}")
        
        return result
    
    def optimize_all_databases(self) -> Dict[str, Any]:
        """Optimize all databases for performance and space."""
        logger.info("Starting comprehensive database optimization...")
        
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'success': True,
            'databases_optimized': 0,
            'total_space_reclaimed_mb': 0,
            'errors': [],
            'details': []
        }
        
        # Get all databases
        databases = [
            "./erdb_main.db",
            "./chat_history.db",
            "./user_management.db",
            "./scraped_pages.db",
            "./data/content_db.sqlite",
            "./data/erdb.db",
            "./data/unified_chroma/chroma.sqlite3"
        ]
        
        for db_path in databases:
            if not os.path.exists(db_path):
                continue
            
            try:
                logger.info(f"Optimizing database: {db_path}")
                
                # Get size before optimization
                size_before = os.path.getsize(db_path) / (1024 * 1024)
                
                # Perform optimization
                opt_result = self._optimize_single_database(db_path)
                
                if opt_result['success']:
                    results['databases_optimized'] += 1
                    space_reclaimed = opt_result.get('space_reclaimed_mb', 0)
                    results['total_space_reclaimed_mb'] += space_reclaimed
                    
                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'size_before_mb': size_before,
                        'size_after_mb': opt_result.get('size_after_mb', size_before),
                        'space_reclaimed_mb': space_reclaimed,
                        'success': True
                    })
                    
                    logger.info(f"Optimized {db_path}: reclaimed {space_reclaimed:.2f} MB")
                else:
                    results['errors'].append(f"{os.path.basename(db_path)}: {opt_result.get('error', 'Unknown error')}")
                    results['details'].append({
                        'database': os.path.basename(db_path),
                        'path': db_path,
                        'success': False,
                        'error': opt_result.get('error', 'Unknown error')
                    })
                    
            except Exception as e:
                error_msg = f"Error optimizing {db_path}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)
                results['success'] = False
        
        logger.info(f"Database optimization complete. Reclaimed {results['total_space_reclaimed_mb']:.2f} MB total")
        return results
    
    def _optimize_single_database(self, db_path: str) -> Dict[str, Any]:
        """Optimize a single database."""
        result = {
            'success': False,
            'space_reclaimed_mb': 0,
            'size_after_mb': 0,
            'error': None
        }
        
        try:
            # Get size before
            size_before = os.path.getsize(db_path)
            size_before_mb = size_before / (1024 * 1024)
            
            # Check if it's a SQLite database
            if not self._is_sqlite_database(db_path):
                result['success'] = True
                result['size_after_mb'] = size_before_mb
                return result
            
            # Perform SQLite-specific optimization
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Set optimization pragmas
            cursor.execute("PRAGMA journal_mode = WAL")
            cursor.execute("PRAGMA synchronous = NORMAL")
            cursor.execute("PRAGMA cache_size = 10000")
            cursor.execute("PRAGMA temp_store = MEMORY")
            cursor.execute("PRAGMA mmap_size = 268435456")
            cursor.execute("PRAGMA foreign_keys = ON")
            cursor.execute("PRAGMA busy_timeout = 30000")
            cursor.execute("PRAGMA wal_autocheckpoint = 1000")
            
            # Run VACUUM
            logger.info(f"Running VACUUM on {db_path}")
            cursor.execute("VACUUM")
            
            # Run ANALYZE
            cursor.execute("ANALYZE")
            
            # Run PRAGMA optimize
            cursor.execute("PRAGMA optimize")
            
            conn.close()
            
            # Get size after
            size_after = os.path.getsize(db_path)
            size_after_mb = size_after / (1024 * 1024)
            space_reclaimed = size_before - size_after
            space_reclaimed_mb = space_reclaimed / (1024 * 1024)
            
            result['success'] = True
            result['space_reclaimed_mb'] = space_reclaimed_mb
            result['size_after_mb'] = size_after_mb
            
            logger.info(f"Optimized {db_path}: {size_before_mb:.2f} MB -> {size_after_mb:.2f} MB (reclaimed {space_reclaimed_mb:.2f} MB)")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Error optimizing {db_path}: {str(e)}")
        
        return result
    
    def get_baseline_sizes(self) -> Dict[str, Any]:
        """Get baseline sizes for all databases."""
        baseline_file = './database_baselines.json'
        
        if not os.path.exists(baseline_file):
            logger.warning("No baseline file found. Creating new baselines...")
            return self._create_baselines()
        
        try:
            with open(baseline_file, 'r') as f:
                baselines = json.load(f)
            return baselines
        except Exception as e:
            logger.error(f"Error loading baselines: {str(e)}")
            return self._create_baselines()
    
    def _create_baselines(self) -> Dict[str, Any]:
        """Create new baseline sizes."""
        logger.info("Creating new database baselines...")
        
        analysis = self.analyze_database_sizes()
        
        baselines = {
            'established_at': datetime.datetime.now().isoformat(),
            'databases': {}
        }
        
        for name, db_info in analysis['databases'].items():
            if db_info['exists']:
                baselines['databases'][name] = {
                    'baseline_size_mb': db_info['size_mb'],
                    'baseline_size_bytes': db_info['size_bytes'],
                    'path': db_info['path'],
                    'tables': db_info.get('tables', []),
                    'row_counts': db_info.get('row_counts', {})
                }
        
        # Save baselines
        try:
            with open('./database_baselines.json', 'w') as f:
                json.dump(baselines, f, indent=2)
            logger.info("Baselines created and saved")
        except Exception as e:
            logger.error(f"Error saving baselines: {str(e)}")
        
        return baselines

# Global health monitor instance
_health_monitor: Optional[HealthMonitor] = None

def get_health_monitor() -> HealthMonitor:
    """Get the global health monitor instance."""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor()
    return _health_monitor

def check_system_health() -> HealthStatus:
    """Quick health check function."""
    monitor = get_health_monitor()
    return monitor.check_health_status()

def get_system_metrics() -> SystemMetrics:
    """Get current system metrics."""
    monitor = get_health_monitor()
    return monitor.get_system_metrics() 