{% extends "admin_base.html" %}

{% block title %}Database Cleanup & Size Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database"></i> Database Cleanup & Size Management
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-chart-pie"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Database Size</span>
                                    <span class="info-box-number" id="total-size">{{ "%.2f"|format(size_analysis.summary.total_size_mb) }} MB</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-database"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Active Databases</span>
                                    <span class="info-box-number">{{ size_analysis.summary.active_databases }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" onclick="analyzeSizes()">
                                    <i class="fas fa-search"></i> Analyze Sizes
                                </button>
                                <button type="button" class="btn btn-success" onclick="cleanupDatabases()">
                                    <i class="fas fa-broom"></i> Cleanup Databases
                                </button>
                                <button type="button" class="btn btn-warning" onclick="optimizeDatabases()">
                                    <i class="fas fa-cogs"></i> Optimize All
                                </button>
                                <button type="button" class="btn btn-info" onclick="establishBaselines()">
                                    <i class="fas fa-flag"></i> Establish Baselines
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Database Details Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Database</th>
                                    <th>Size (MB)</th>
                                    <th>Baseline (MB)</th>
                                    <th>Change (MB)</th>
                                    <th>Change (%)</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="database-table-body">
                                {% for name, db_info in size_analysis.databases.items() %}
                                <tr>
                                    <td>
                                        <strong>{{ name }}</strong><br>
                                        <small class="text-muted">{{ db_info.path }}</small>
                                    </td>
                                    <td>{{ "%.2f"|format(db_info.size_mb) }}</td>
                                    <td>
                                        {% if name in baselines.databases %}
                                            {{ "%.2f"|format(baselines.databases[name].baseline_size_mb) }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if name in changes %}
                                            {% set change = changes[name].change_mb %}
                                            <span class="badge badge-{{ 'success' if change < 0 else 'danger' if change > 0 else 'secondary' }}">
                                                {{ "%.2f"|format(change) }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if name in changes %}
                                            {% set change_pct = changes[name].change_percentage %}
                                            <span class="badge badge-{{ 'success' if change_pct < 0 else 'danger' if change_pct > 0 else 'secondary' }}">
                                                {{ "%.1f"|format(change_pct) }}%
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if db_info.get('vacuum_needed', False) %}
                                            <span class="badge badge-warning">Vacuum Needed</span>
                                        {% elif db_info.get('integrity_check', True) %}
                                            <span class="badge badge-success">OK</span>
                                        {% else %}
                                            <span class="badge badge-danger">Issues</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="cleanupDatabase('{{ name }}')">
                                            <i class="fas fa-broom"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="optimizeDatabase('{{ name }}')">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Recommendations -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">
                                        <i class="fas fa-lightbulb"></i> Recommendations
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul id="recommendations-list">
                                        <!-- Recommendations will be populated by JavaScript -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2" id="loading-message">Processing...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentAnalysis = {{ size_analysis | tojson }};
let currentBaselines = {{ baselines | tojson }};

function showLoading(message = 'Processing...') {
    document.getElementById('loading-message').textContent = message;
    $('#loadingModal').modal('show');
}

function hideLoading() {
    $('#loadingModal').modal('hide');
}

function showAlert(message, type = 'success') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    // Insert at top of card body
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

async function analyzeSizes() {
    showLoading('Analyzing database sizes...');
    
    try {
        const response = await fetch('/admin/health/api/analyze-sizes');
        const data = await response.json();
        
        if (data.success) {
            currentAnalysis = data.size_analysis;
            currentBaselines = data.baselines;
            updateTable();
            showAlert('Database analysis completed successfully');
        } else {
            showAlert('Error analyzing database sizes: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error analyzing database sizes: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function cleanupDatabases() {
    if (!confirm('Are you sure you want to clean up all databases? This will reclaim space but may take some time.')) {
        return;
    }
    
    showLoading('Cleaning up databases...');
    
    try {
        const response = await fetch('/admin/health/api/database-cleanup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        });
        const data = await response.json();
        
        if (data.success) {
            showAlert(data.message);
            // Refresh the analysis
            await analyzeSizes();
        } else {
            showAlert('Error cleaning up databases: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error cleaning up databases: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function optimizeDatabases() {
    if (!confirm('Are you sure you want to optimize all databases? This will improve performance and reclaim space.')) {
        return;
    }
    
    showLoading('Optimizing databases...');
    
    try {
        const response = await fetch('/admin/health/api/optimize-all-databases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        });
        const data = await response.json();
        
        if (data.success) {
            showAlert(data.message);
            // Refresh the analysis
            await analyzeSizes();
        } else {
            showAlert('Error optimizing databases: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error optimizing databases: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function establishBaselines() {
    if (!confirm('Are you sure you want to establish new baselines? This will overwrite existing baselines.')) {
        return;
    }
    
    showLoading('Establishing new baselines...');
    
    try {
        const response = await fetch('/admin/health/api/establish-baselines', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        });
        const data = await response.json();
        
        if (data.success) {
            showAlert(data.message);
            currentBaselines = data.baselines;
            updateTable();
        } else {
            showAlert('Error establishing baselines: ' + data.error, 'danger');
        }
    } catch (error) {
        showAlert('Error establishing baselines: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

function updateTable() {
    const tbody = document.getElementById('database-table-body');
    tbody.innerHTML = '';
    
    for (const [name, db_info] of Object.entries(currentAnalysis.databases)) {
        const baseline = currentBaselines.databases?.[name];
        const change = baseline ? {
            change_mb: db_info.size_mb - baseline.baseline_size_mb,
            change_percentage: baseline.baseline_size_mb > 0 ? 
                ((db_info.size_mb - baseline.baseline_size_mb) / baseline.baseline_size_mb * 100) : 0
        } : null;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${name}</strong><br>
                <small class="text-muted">${db_info.path}</small>
            </td>
            <td>${db_info.size_mb.toFixed(2)}</td>
            <td>${baseline ? baseline.baseline_size_mb.toFixed(2) : '-'}</td>
            <td>
                ${change ? `
                    <span class="badge badge-${change.change_mb < 0 ? 'success' : change.change_mb > 0 ? 'danger' : 'secondary'}">
                        ${change.change_mb.toFixed(2)}
                    </span>
                ` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                ${change ? `
                    <span class="badge badge-${change.change_percentage < 0 ? 'success' : change.change_percentage > 0 ? 'danger' : 'secondary'}">
                        ${change.change_percentage.toFixed(1)}%
                    </span>
                ` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                ${db_info.vacuum_needed ? 
                    '<span class="badge badge-warning">Vacuum Needed</span>' :
                    db_info.integrity_check ? 
                        '<span class="badge badge-success">OK</span>' :
                        '<span class="badge badge-danger">Issues</span>'
                }
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="cleanupDatabase('${name}')">
                    <i class="fas fa-broom"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-warning" onclick="optimizeDatabase('${name}')">
                    <i class="fas fa-cog"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    }
    
    // Update total size
    document.getElementById('total-size').textContent = currentAnalysis.summary.total_size_mb.toFixed(2) + ' MB';
}

function cleanupDatabase(name) {
    if (!confirm(`Are you sure you want to clean up the ${name} database?`)) {
        return;
    }
    
    showLoading(`Cleaning up ${name} database...`);
    
    // This would need a specific endpoint for individual database cleanup
    // For now, we'll use the general cleanup
    cleanupDatabases();
}

function optimizeDatabase(name) {
    if (!confirm(`Are you sure you want to optimize the ${name} database?`)) {
        return;
    }
    
    showLoading(`Optimizing ${name} database...`);
    
    // This would need a specific endpoint for individual database optimization
    // For now, we'll use the general optimization
    optimizeDatabases();
}

// Initialize recommendations
function updateRecommendations() {
    const recommendationsList = document.getElementById('recommendations-list');
    recommendationsList.innerHTML = '';
    
    const recommendations = [];
    
    // Check for large databases
    for (const [name, db_info] of Object.entries(currentAnalysis.databases)) {
        if (db_info.size_mb > 100) {
            recommendations.push(`Database ${name} is large (${db_info.size_mb.toFixed(1)} MB). Consider archiving old data.`);
        }
        
        if (db_info.vacuum_needed) {
            const freePercentage = (db_info.free_pages / db_info.total_pages * 100).toFixed(1);
            recommendations.push(`Database ${name} needs VACUUM operation (${freePercentage}% free pages).`);
        }
        
        if (!db_info.integrity_check) {
            recommendations.push(`Database ${name} has integrity issues. Run integrity check.`);
        }
    }
    
    if (recommendations.length === 0) {
        recommendations.push('All databases are in good condition.');
    }
    
    recommendations.forEach(rec => {
        const li = document.createElement('li');
        li.textContent = rec;
        recommendationsList.appendChild(li);
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateRecommendations();
});
</script>
{% endblock %}
