"""
User service facade module.

This module provides a unified interface for all user management functionality
by importing and re-exporting functions from various service modules.
"""

import os
import logging

# Import modules
from app.utils import db_connection as db
from app.utils import security
from app.routes import auth
from app.utils import permissions
from app.models import user as user_models

# Import user CRUD operations from user_management
from app.services.user_management import (
    get_user_by_id,
    get_user_by_username,
    get_user_by_email,
    get_all_users,
    register_user,
    send_verification_email,
    log_user_activity,
    # authenticate_user,  # Use the comprehensive version from auth module instead
    update_session_end,
    verify_email,
    generate_password_reset_token,
    send_password_reset_email,
    verify_password_reset_token,
    reset_password,
    update_user,
    delete_user,
    bulk_update_users,
    update_user_permissions,
    update_dashboard_permission,
    update_permission_override,
    update_user_group,
    get_permission_groups,
    update_group_permission,
    get_user_activity_logs,
    get_permission_audit_logs,
    USER_DB_PATH,
    PROFILE_PICS_DIR,
    EMAIL_VERIFY_EXPIRY,
    PASSWORD_RESET_EXPIRY
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Re-export constants
USER_DB_PATH = USER_DB_PATH
PROFILE_PICS_DIR = PROFILE_PICS_DIR
EMAIL_VERIFY_EXPIRY = EMAIL_VERIFY_EXPIRY
PASSWORD_RESET_EXPIRY = PASSWORD_RESET_EXPIRY

# Re-export functions from db module
init_user_db = db.initialize_database

# Re-export functions from auth module
login_user = auth.login
logout_user = auth.logout
init_login_manager = auth.init_login_manager
create_user_session = auth.create_user_session

# Re-export functions from security module
init_csrf = security.init_csrf
init_limiter = security.init_limiter
hash_password = security.hash_password
verify_password = security.verify_password
validate_password_complexity = security.validate_password_complexity
generate_reset_token = security.generate_reset_token
verify_reset_token = security.verify_reset_token
generate_verification_token = security.generate_verification_token
MAX_LOGIN_ATTEMPTS = security.MAX_LOGIN_ATTEMPTS
LOCKOUT_WINDOW = security.LOCKOUT_WINDOW
PASSWORD_EXPIRY_DAYS = security.PASSWORD_EXPIRY_DAYS

# Re-export functions from permissions module
function_permission_required = permissions.function_permission_required
admin_required = permissions.admin_required
get_dashboard_functions = permissions.get_dashboard_functions
create_permission_group = permissions.create_permission_group
update_permission_group = permissions.update_permission_group
log_permission_change = permissions.log_permission_change

# Re-export classes from user_models module
User = user_models.User
PermissionGroup = user_models.PermissionGroup

# Re-export user CRUD functions
get_user_by_id = get_user_by_id
get_user_by_username = get_user_by_username
get_user_by_email = get_user_by_email
get_all_users = get_all_users
register_user = register_user
send_verification_email = send_verification_email
log_user_activity = log_user_activity

# Re-export authentication functions - use comprehensive version from auth module
authenticate_user = auth.authenticate_user
update_session_end = update_session_end
verify_email = verify_email
generate_password_reset_token = generate_password_reset_token
send_password_reset_email = send_password_reset_email
verify_password_reset_token = verify_password_reset_token
reset_password = reset_password

# Re-export user management functions
update_user = update_user
delete_user = delete_user
bulk_update_users = bulk_update_users
update_user_permissions = update_user_permissions
update_dashboard_permission = update_dashboard_permission
update_permission_override = update_permission_override
update_user_group = update_user_group

# Re-export permission management functions
get_permission_groups = get_permission_groups
update_group_permission = update_group_permission

# Re-export logging functions
get_user_activity_logs = get_user_activity_logs
get_permission_audit_logs = get_permission_audit_logs

# Stub function to maintain backward compatibility
def get_user_details(user_id):
    """Get user details by ID (alias for get_user_by_id)."""
    return get_user_by_id(user_id)

# Custom user loader for Flask-Login
def load_user(user_id):
    """Load user by ID for Flask-Login."""
    return get_user_by_id(user_id)

# Legacy function for backward compatibility
def init_user_db_legacy():
    """Initialize the user database with all required tables."""
    try:
        # Create profile pictures directory if it doesn't exist
        os.makedirs(PROFILE_PICS_DIR, exist_ok=True)

        # Call the new initialization function
        return db.initialize_database()
    except Exception as e:
        logger.error(f"Database initialization error: {str(e)}")
        return False 