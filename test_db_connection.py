#!/usr/bin/env python3
"""
Test script to check database connection and query execution.
"""

import os
import sys
import sqlite3

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils import db_connection as db

def test_db_connection():
    """Test database connection and query execution."""
    
    print("Testing database connection and query execution...")
    
    # Test direct connection
    try:
        conn = sqlite3.connect("./user_management.db")
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT username FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result:
            print(f"✅ Direct query successful: {result[0]}")
        else:
            print("❌ Direct query failed - no results")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Direct connection failed: {str(e)}")
    
    # Test through db module
    try:
        print(f"\nTesting through db module...")
        print(f"USER_DB_PATH from db module: {db.USER_DB_PATH}")
        
        # Test query through db module
        user_data = db.execute_query("SELECT username FROM users WHERE username = ?", ("admin",))
        
        if user_data:
            print(f"✅ DB module query successful: {user_data[0]['username']}")
        else:
            print("❌ DB module query failed - no results")
            
    except Exception as e:
        print(f"❌ DB module query failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_db_connection()
