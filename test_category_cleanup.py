#!/usr/bin/env python3
"""
Test script for category cleanup functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.helpers import check_category_has_related_data, cleanup_empty_category_data

def test_category_cleanup():
    """Test the category cleanup functionality."""
    
    # Test with a category that likely has data
    test_category = "CANOPY"  # This is likely to have data
    
    print(f"Testing category cleanup for: {test_category}")
    print("=" * 50)
    
    # Check for related data before cleanup
    print("Before cleanup:")
    data_check = check_category_has_related_data(test_category)
    print(f"Has data: {data_check['has_data']}")
    print(f"Total items: {data_check['total_items']}")
    print("\nData summary:")
    for key, value in data_check['data_summary'].items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 50)
    
    # Test cleanup
    print(f"Testing cleanup for category: {test_category}")
    cleanup_result = cleanup_empty_category_data(test_category)
    
    print(f"Success: {cleanup_result['success']}")
    print(f"Category empty: {cleanup_result.get('category_empty', False)}")
    print(f"Chat cleaned: {cleanup_result.get('chat_cleaned', False)}")
    print(f"Message: {cleanup_result['message']}")
    
    if cleanup_result.get('chat_cleaned', False):
        print("✅ Chat data cleanup successful")
        cleanup_details = cleanup_result.get('cleanup_details', {})
        if cleanup_details:
            print(f"  - Chat history deleted: {cleanup_details.get('chat_history_deleted', 0)}")
            print(f"  - Analytics deleted: {cleanup_details.get('analytics_deleted', 0)}")
    else:
        print("ℹ️  No cleanup needed (category still has files)")
    
    print("\n" + "=" * 50)
    
    # Check for related data after cleanup
    print("After cleanup:")
    data_check_after = check_category_has_related_data(test_category)
    print(f"Has data: {data_check_after['has_data']}")
    print(f"Total items: {data_check_after['total_items']}")
    print("\nData summary:")
    for key, value in data_check_after['data_summary'].items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_category_cleanup() 