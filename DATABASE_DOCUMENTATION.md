# ERDB System Database Documentation

*Generated on: 2025-08-07 07:21:26*

## Executive Summary

- **Total Databases**: 11
- **Total Size**: 953.65 MB
- **Total Tables**: 122
- **Vector Databases**: 1

## Database Details

### chat_history.db

- **Path**: `.\chat_history.db`
- **Size**: 2.82 MB (2953216 bytes)
- **Tables**: 5
- **Page Count**: 721
- **Page Size**: 4096 bytes

#### Tables

##### chat_history

- **Rows**: 325
- **Columns**: 21
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| category | TEXT | False | NULL | False |
| question | TEXT | False | NULL | False |
| answer | TEXT | False | NULL | False |
| sources | TEXT | False | NULL | False |
| timestamp | DATETIME | False | CURRENT_TIMESTAMP | False |
| images | TEXT | False | NULL | False |
| pdf_links | TEXT | False | NULL | False |
| metadata | TEXT | False | NULL | False |
| url_images | TEXT | False | NULL | False |
| pdf_images | TEXT | False | NULL | False |
| document_thumbnails | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| session_id | TEXT | False | NULL | False |
| session_start | DATETIME | False | NULL | False |
| session_end | DATETIME | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| anti_hallucination_mode | TEXT | False | NULL | False |
| model_name | TEXT | False | NULL | False |
| embedding_model | TEXT | False | NULL | False |
| vision_model | TEXT | False | NULL | False |

##### sqlite_sequence

- **Rows**: 4
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| name |  | False | NULL | False |
| seq |  | False | NULL | False |

##### chat_analytics

- **Rows**: 323
- **Columns**: 28
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| session_id | TEXT | False | NULL | False |
| chat_id | INTEGER | False | NULL | False |
| category | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| question_length | INTEGER | False | NULL | False |
| answer_length | INTEGER | False | NULL | False |
| processing_time | REAL | False | NULL | False |
| source_count | INTEGER | False | NULL | False |
| image_count | INTEGER | False | NULL | False |
| token_count | INTEGER | False | NULL | False |
| timestamp | DATETIME | False | CURRENT_TIMESTAMP | False |
| model_name | TEXT | False | NULL | False |
| embedding_model | TEXT | False | NULL | False |
| vision_model | TEXT | False | NULL | False |
| vision_enabled | BOOLEAN | False | NULL | False |
| images_filtered | INTEGER | False | NULL | False |
| total_images_extracted | INTEGER | False | NULL | False |
| filter_sensitivity | TEXT | False | NULL | False |
| hallucination_detected | BOOLEAN | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| country | TEXT | False | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| anti_hallucination_mode | TEXT | False | NULL | False |

##### geoip_analytics

- **Rows**: 573
- **Columns**: 13
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| ip_address | TEXT | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| country | TEXT | False | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| timestamp | DATETIME | False | CURRENT_TIMESTAMP | False |
| user_agent | TEXT | False | NULL | False |
| page_url | TEXT | False | NULL | False |
| session_id | TEXT | False | NULL | False |

##### session_metadata

- **Rows**: 1
- **Columns**: 12
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| session_id | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| first_visit_timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| session_count | INTEGER | False | 1 | False |
| timezone | TEXT | False | NULL | False |
| local_time | TEXT | False | NULL | False |
| greeting_type | TEXT | False | NULL | False |
| time_of_day | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_session_metadata_1 | True | session_id |

---

### erdb_main.db

- **Path**: `.\erdb_main.db`
- **Size**: 0.88 MB (925696 bytes)
- **Tables**: 29
- **Page Count**: 226
- **Page Size**: 4096 bytes

#### Tables

##### chat_history

- **Rows**: 123
- **Columns**: 12
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| category | VARCHAR(100) | True | NULL | False |
| question | TEXT | True | NULL | False |
| answer | TEXT | True | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| session_id | VARCHAR(255) | False | NULL | False |
| client_name | VARCHAR(100) | False | NULL | False |
| device_fingerprint | VARCHAR(255) | False | NULL | False |
| anti_hallucination_mode | VARCHAR(20) | False | 'strict' | False |
| model_name | VARCHAR(100) | False | NULL | False |
| embedding_model | VARCHAR(100) | False | NULL | False |
| vision_model | VARCHAR(100) | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_chat_history_timestamp | False | timestamp |
| idx_chat_history_category | False | category |

##### sqlite_sequence

- **Rows**: 19
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| name |  | False | NULL | False |
| seq |  | False | NULL | False |

##### chat_analytics

- **Rows**: 123
- **Columns**: 28
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| chat_id | INTEGER | False | NULL | False |
| session_id | VARCHAR(255) | False | NULL | False |
| category | VARCHAR(100) | False | NULL | False |
| client_name | VARCHAR(100) | False | NULL | False |
| question_length | INTEGER | False | NULL | False |
| answer_length | INTEGER | False | NULL | False |
| processing_time | REAL | False | NULL | False |
| source_count | INTEGER | False | NULL | False |
| image_count | INTEGER | False | NULL | False |
| token_count | INTEGER | False | NULL | False |
| model_name | VARCHAR(100) | False | NULL | False |
| embedding_model | VARCHAR(100) | False | NULL | False |
| vision_model | VARCHAR(100) | False | NULL | False |
| vision_enabled | BOOLEAN | False | NULL | False |
| images_filtered | INTEGER | False | NULL | False |
| total_images_extracted | INTEGER | False | NULL | False |
| filter_sensitivity | VARCHAR(20) | False | NULL | False |
| hallucination_detected | BOOLEAN | False | NULL | False |
| anti_hallucination_mode | VARCHAR(20) | False | NULL | False |
| device_fingerprint | VARCHAR(255) | False | NULL | False |
| ip_address | VARCHAR(45) | False | NULL | False |
| city | VARCHAR(100) | False | NULL | False |
| region | VARCHAR(100) | False | NULL | False |
| country | VARCHAR(100) | False | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_chat_analytics_chat_id | False | chat_id |

##### content_sources

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | False | NULL | False |
| title | TEXT | False | NULL | False |
| content | TEXT | False | NULL | False |
| category | VARCHAR(100) | False | NULL | False |
| source_type | VARCHAR(50) | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_updated | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| metadata | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_content_sources_url | False | url |

##### scraped_pages

- **Rows**: 0
- **Columns**: 6
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | True | NULL | False |
| filename | VARCHAR(255) | True | NULL | False |
| category | VARCHAR(100) | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_scraped | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

##### sqlite_stat1

- **Rows**: 49
- **Columns**: 3
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tbl |  | False | NULL | False |
| idx |  | False | NULL | False |
| stat |  | False | NULL | False |

##### source_urls

- **Rows**: 7
- **Columns**: 9
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | True | NULL | False |
| title | TEXT | False | NULL | False |
| description | TEXT | False | NULL | False |
| last_scraped | TIMESTAMP | False | NULL | False |
| last_updated | TIMESTAMP | False | NULL | False |
| status | TEXT | False | NULL | False |
| error_message | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_source_urls_1 | True | url |

##### url_content

- **Rows**: 0
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| source_url_id | INTEGER | True | NULL | False |
| content_type | TEXT | True | NULL | False |
| content | TEXT | True | NULL | False |
| content_order | INTEGER | True | NULL | False |
| metadata | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_url_content_content_type | False | content_type |
| idx_url_content_source_url_id | False | source_url_id |

##### cover_images

- **Rows**: 0
- **Columns**: 7
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| pdf_document_id | INTEGER | True | NULL | False |
| image_path | TEXT | True | NULL | False |
| image_url | TEXT | True | NULL | False |
| source | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_cover_images_pdf_document_id | True | pdf_document_id |

##### database_version

- **Rows**: 5
- **Columns**: 4
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| version | INTEGER | True | NULL | False |
| applied_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| description | TEXT | False | NULL | False |

##### greeting_templates

- **Rows**: 21
- **Columns**: 8
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| template_type | TEXT | True | NULL | False |
| greeting_text | TEXT | True | NULL | False |
| context_conditions | TEXT | False | NULL | False |
| is_active | BOOLEAN | False | 1 | False |
| weight | INTEGER | False | 1 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_greeting_templates_active | False | is_active |
| idx_greeting_templates_type | False | template_type |

##### user_greeting_preferences

- **Rows**: 0
- **Columns**: 4
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| user_id | INTEGER | False | NULL | True |
| preferred_greeting_style | TEXT | False | 'friendly' | False |
| last_greeting_used | INTEGER | False | NULL | False |
| greeting_frequency | TEXT | False | 'every_response' | False |

##### greeting_analytics

- **Rows**: 415
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| session_id | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| greeting_template_id | INTEGER | False | NULL | False |
| greeting_type | TEXT | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| user_response_time | REAL | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_greeting_analytics_timestamp | False | timestamp |
| idx_greeting_analytics_session | False | session_id |

##### extracted_locations

- **Rows**: 327
- **Columns**: 16
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_text | TEXT | True | NULL | False |
| location_type | TEXT | True | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| confidence_score | REAL | False | 0.0 | False |
| context_snippet | TEXT | False | NULL | False |
| geocoded_address | TEXT | False | NULL | False |
| country | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |
| municipality | TEXT | False | NULL | False |
| barangay | TEXT | False | NULL | False |
| administrative_level | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_extracted_locations_coordinates | False | latitude, longitude |
| idx_extracted_locations_type | False | location_type |

##### location_sources

- **Rows**: 327
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_id | INTEGER | True | NULL | False |
| source_type | TEXT | True | NULL | False |
| source_id | INTEGER | True | NULL | False |
| page_number | INTEGER | False | NULL | False |
| extraction_method | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_location_sources_location_id | False | location_id |
| idx_location_sources_type | False | source_type |

##### geocoding_cache

- **Rows**: 97
- **Columns**: 13
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_query | TEXT | True | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| formatted_address | TEXT | False | NULL | False |
| geocoding_service | TEXT | False | 'nominatim' | False |
| confidence_score | REAL | False | 0.0 | False |
| cached_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| expires_at | TIMESTAMP | False | NULL | False |
| status | TEXT | False | 'success' | False |
| country | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_geocoding_cache_expires | False | expires_at |
| idx_geocoding_cache_query | False | location_query |
| sqlite_autoindex_geocoding_cache_1 | True | location_query |

##### users

- **Rows**: 3
- **Columns**: 19
- **Indexes**: 7

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| user_id | INTEGER | False | NULL | True |
| username | TEXT | True | NULL | False |
| password_hash | TEXT | True | NULL | False |
| email | TEXT | True | NULL | False |
| role | TEXT | True | NULL | False |
| group_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_login | TIMESTAMP | False | NULL | False |
| account_status | TEXT | False | 'pending' | False |
| failed_login_attempts | INTEGER | False | 0 | False |
| reset_token | TEXT | False | NULL | False |
| reset_token_expiry | TIMESTAMP | False | NULL | False |
| email_verified | BOOLEAN | False | 0 | False |
| verification_token | TEXT | False | NULL | False |
| verification_token_expiry | TIMESTAMP | False | NULL | False |
| password_changed_at | TIMESTAMP | False | NULL | False |
| profile_picture | TEXT | False | NULL | False |
| full_name | TEXT | False | NULL | False |
| notification_preferences | TEXT | False | '{"email_alerts": true, "security_notifications": true}' | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_users_group_id | False | group_id |
| idx_users_account_status | False | account_status |
| idx_users_role | False | role |
| idx_users_email | False | email |
| idx_users_username | False | username |
| sqlite_autoindex_users_2 | True | email |
| sqlite_autoindex_users_1 | True | username |

##### permission_groups

- **Rows**: 4
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| group_id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_groups_name | False | name |
| sqlite_autoindex_permission_groups_1 | True | name |

##### group_permissions

- **Rows**: 60
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| group_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_group_permissions_function | False | function_name |
| idx_group_permissions_group_id | False | group_id |
| sqlite_autoindex_group_permissions_1 | True | group_id, function_name |

##### permission_overrides

- **Rows**: 17
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_overrides_function | False | function_name |
| idx_permission_overrides_user_id | False | user_id |
| sqlite_autoindex_permission_overrides_1 | True | user_id, function_name |

##### permission_audit_logs

- **Rows**: 27
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| admin_user_id | INTEGER | False | NULL | False |
| target_user_id | INTEGER | False | NULL | False |
| change_type | TEXT | True | NULL | False |
| entity_changed | TEXT | True | NULL | False |
| old_value | TEXT | False | NULL | False |
| new_value | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_audit_logs_change_type | False | change_type |
| idx_permission_audit_logs_timestamp | False | timestamp |
| idx_permission_audit_logs_target_user_id | False | target_user_id |
| idx_permission_audit_logs_admin_user_id | False | admin_user_id |

##### dashboard_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_dashboard_permissions_function | False | function_name |
| idx_dashboard_permissions_user_id | False | user_id |
| sqlite_autoindex_dashboard_permissions_1 | True | user_id, function_name |

##### category_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| category | TEXT | True | NULL | False |
| permission | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_category_permissions_category | False | category |
| idx_category_permissions_user_id | False | user_id |
| sqlite_autoindex_category_permissions_1 | True | user_id, category |

##### user_activity_logs

- **Rows**: 11
- **Columns**: 9
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| ip_address | TEXT | False | NULL | False |
| action_type | TEXT | True | NULL | False |
| details | TEXT | False | NULL | False |
| resource_type | TEXT | False | NULL | False |
| resource_id | TEXT | False | NULL | False |
| status | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_activity_logs_timestamp | False | timestamp |
| idx_user_activity_logs_action_type | False | action_type |
| idx_user_activity_logs_user_id | False | user_id |

##### user_sessions

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| session_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| session_token | TEXT | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |
| user_agent | TEXT | False | NULL | False |
| start_time | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| end_time | TIMESTAMP | False | NULL | False |
| is_active | BOOLEAN | False | 1 | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_sessions_device_fingerprint | False | device_fingerprint |
| idx_user_sessions_session_token | False | session_token |
| idx_user_sessions_user_id | False | user_id |
| sqlite_autoindex_user_sessions_1 | True | session_token |

##### forms

- **Rows**: 1
- **Columns**: 7
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| fields | TEXT | True | NULL | False |
| is_active | BOOLEAN | False | 1 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_forms_active | False | is_active |

##### form_submissions

- **Rows**: 21
- **Columns**: 8
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| form_id | INTEGER | True | NULL | False |
| user_id | INTEGER | False | NULL | False |
| pdf_document_id | INTEGER | True | NULL | False |
| submission_data | TEXT | True | NULL | False |
| ip_address | TEXT | False | NULL | False |
| user_agent | TEXT | False | NULL | False |
| submitted_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_form_submissions_submitted_at | False | submitted_at |
| idx_form_submissions_pdf_document_id | False | pdf_document_id |
| idx_form_submissions_form_id | False | form_id |

##### categories

- **Rows**: 3
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| form_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_categories_form_id | False | form_id |
| sqlite_autoindex_categories_1 | True | name |

##### pdf_documents

- **Rows**: 63
- **Columns**: 27
- **Indexes**: 6

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| filename | TEXT | True | NULL | False |
| original_filename | TEXT | True | NULL | False |
| category | TEXT | True | NULL | False |
| upload_date | TIMESTAMP | True | NULL | False |
| source_url_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| form_id | INTEGER | False | NULL | False |
| file_size | INTEGER | False | NULL | False |
| page_count | INTEGER | False | NULL | False |
| updated_at | TIMESTAMP | False | NULL | False |
| download_filename | TEXT | False | NULL | False |
| has_non_ocr_version | BOOLEAN | False | FALSE | False |
| conversion_settings | TEXT | False | NULL | False |
| published_year | INTEGER | False | NULL | False |
| published_month_start | INTEGER | False | NULL | False |
| published_month_end | INTEGER | False | NULL | False |
| published_month_range_str | TEXT | False | NULL | False |
| pdf_title | TEXT | False | NULL | False |
| pdf_author | TEXT | False | NULL | False |
| pdf_subject | TEXT | False | NULL | False |
| pdf_keywords | TEXT | False | NULL | False |
| pdf_creation_date | TIMESTAMP | False | NULL | False |
| pdf_modification_date | TIMESTAMP | False | NULL | False |
| pdf_version | TEXT | False | NULL | False |
| pdf_producer | TEXT | False | NULL | False |
| pdf_creator | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_pdf_documents_subject | False | pdf_subject |
| idx_pdf_documents_author | False | pdf_author |
| idx_pdf_documents_title | False | pdf_title |
| idx_pdf_documents_form_id | False | form_id |
| idx_pdf_documents_category | False | category |
| idx_pdf_documents_filename | False | filename |

---

### erdb_main_corrupted_20250805_155401.db

**Error**: database disk image is malformed

### erdb_main_corrupted_20250805_155813.db

- **Path**: `.\erdb_main_corrupted_20250805_155813.db`
- **Size**: 0.77 MB (806912 bytes)
- **Tables**: 29
- **Page Count**: 197
- **Page Size**: 4096 bytes

#### Tables

##### chat_history

- **Rows**: 123
- **Columns**: 12
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| category | VARCHAR(100) | True | NULL | False |
| question | TEXT | True | NULL | False |
| answer | TEXT | True | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| session_id | VARCHAR(255) | False | NULL | False |
| client_name | VARCHAR(100) | False | NULL | False |
| device_fingerprint | VARCHAR(255) | False | NULL | False |
| anti_hallucination_mode | VARCHAR(20) | False | 'strict' | False |
| model_name | VARCHAR(100) | False | NULL | False |
| embedding_model | VARCHAR(100) | False | NULL | False |
| vision_model | VARCHAR(100) | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_chat_history_timestamp | False | timestamp |
| idx_chat_history_category | False | category |

##### sqlite_sequence

- **Rows**: 19
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| name |  | False | NULL | False |
| seq |  | False | NULL | False |

##### chat_analytics

- **Rows**: 123
- **Columns**: 28
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| chat_id | INTEGER | False | NULL | False |
| session_id | VARCHAR(255) | False | NULL | False |
| category | VARCHAR(100) | False | NULL | False |
| client_name | VARCHAR(100) | False | NULL | False |
| question_length | INTEGER | False | NULL | False |
| answer_length | INTEGER | False | NULL | False |
| processing_time | REAL | False | NULL | False |
| source_count | INTEGER | False | NULL | False |
| image_count | INTEGER | False | NULL | False |
| token_count | INTEGER | False | NULL | False |
| model_name | VARCHAR(100) | False | NULL | False |
| embedding_model | VARCHAR(100) | False | NULL | False |
| vision_model | VARCHAR(100) | False | NULL | False |
| vision_enabled | BOOLEAN | False | NULL | False |
| images_filtered | INTEGER | False | NULL | False |
| total_images_extracted | INTEGER | False | NULL | False |
| filter_sensitivity | VARCHAR(20) | False | NULL | False |
| hallucination_detected | BOOLEAN | False | NULL | False |
| anti_hallucination_mode | VARCHAR(20) | False | NULL | False |
| device_fingerprint | VARCHAR(255) | False | NULL | False |
| ip_address | VARCHAR(45) | False | NULL | False |
| city | VARCHAR(100) | False | NULL | False |
| region | VARCHAR(100) | False | NULL | False |
| country | VARCHAR(100) | False | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_chat_analytics_chat_id | False | chat_id |

##### content_sources

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | False | NULL | False |
| title | TEXT | False | NULL | False |
| content | TEXT | False | NULL | False |
| category | VARCHAR(100) | False | NULL | False |
| source_type | VARCHAR(50) | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_updated | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| metadata | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_content_sources_url | False | url |

##### scraped_pages

- **Rows**: 0
- **Columns**: 6
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | True | NULL | False |
| filename | VARCHAR(255) | True | NULL | False |
| category | VARCHAR(100) | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_scraped | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

##### sqlite_stat1

- **Rows**: 3
- **Columns**: 3
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tbl |  | False | NULL | False |
| idx |  | False | NULL | False |
| stat |  | False | NULL | False |

##### source_urls

- **Rows**: 7
- **Columns**: 9
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| url | TEXT | True | NULL | False |
| title | TEXT | False | NULL | False |
| description | TEXT | False | NULL | False |
| last_scraped | TIMESTAMP | False | NULL | False |
| last_updated | TIMESTAMP | False | NULL | False |
| status | TEXT | False | NULL | False |
| error_message | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_source_urls_1 | True | url |

##### url_content

- **Rows**: 0
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| source_url_id | INTEGER | True | NULL | False |
| content_type | TEXT | True | NULL | False |
| content | TEXT | True | NULL | False |
| content_order | INTEGER | True | NULL | False |
| metadata | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_url_content_content_type | False | content_type |
| idx_url_content_source_url_id | False | source_url_id |

##### cover_images

- **Rows**: 0
- **Columns**: 7
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| pdf_document_id | INTEGER | True | NULL | False |
| image_path | TEXT | True | NULL | False |
| image_url | TEXT | True | NULL | False |
| source | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_cover_images_pdf_document_id | True | pdf_document_id |

##### database_version

- **Rows**: 5
- **Columns**: 4
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| version | INTEGER | True | NULL | False |
| applied_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| description | TEXT | False | NULL | False |

##### greeting_templates

- **Rows**: 21
- **Columns**: 8
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| template_type | TEXT | True | NULL | False |
| greeting_text | TEXT | True | NULL | False |
| context_conditions | TEXT | False | NULL | False |
| is_active | BOOLEAN | False | 1 | False |
| weight | INTEGER | False | 1 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_greeting_templates_active | False | is_active |
| idx_greeting_templates_type | False | template_type |

##### user_greeting_preferences

- **Rows**: 0
- **Columns**: 4
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| user_id | INTEGER | False | NULL | True |
| preferred_greeting_style | TEXT | False | 'friendly' | False |
| last_greeting_used | INTEGER | False | NULL | False |
| greeting_frequency | TEXT | False | 'every_response' | False |

##### greeting_analytics

- **Rows**: 262
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| session_id | TEXT | False | NULL | False |
| client_name | TEXT | False | NULL | False |
| greeting_template_id | INTEGER | False | NULL | False |
| greeting_type | TEXT | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| user_response_time | REAL | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_greeting_analytics_timestamp | False | timestamp |
| idx_greeting_analytics_session | False | session_id |

##### extracted_locations

- **Rows**: 208
- **Columns**: 16
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_text | TEXT | True | NULL | False |
| location_type | TEXT | True | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| confidence_score | REAL | False | 0.0 | False |
| context_snippet | TEXT | False | NULL | False |
| geocoded_address | TEXT | False | NULL | False |
| country | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |
| municipality | TEXT | False | NULL | False |
| barangay | TEXT | False | NULL | False |
| administrative_level | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_extracted_locations_coordinates | False | latitude, longitude |
| idx_extracted_locations_type | False | location_type |

##### location_sources

- **Rows**: 208
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_id | INTEGER | True | NULL | False |
| source_type | TEXT | True | NULL | False |
| source_id | INTEGER | True | NULL | False |
| page_number | INTEGER | False | NULL | False |
| extraction_method | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_location_sources_location_id | False | location_id |
| idx_location_sources_type | False | source_type |

##### geocoding_cache

- **Rows**: 44
- **Columns**: 13
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| location_query | TEXT | True | NULL | False |
| latitude | REAL | False | NULL | False |
| longitude | REAL | False | NULL | False |
| formatted_address | TEXT | False | NULL | False |
| geocoding_service | TEXT | False | 'nominatim' | False |
| confidence_score | REAL | False | 0.0 | False |
| cached_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| expires_at | TIMESTAMP | False | NULL | False |
| status | TEXT | False | 'success' | False |
| country | TEXT | False | NULL | False |
| region | TEXT | False | NULL | False |
| city | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_geocoding_cache_expires | False | expires_at |
| idx_geocoding_cache_query | False | location_query |
| sqlite_autoindex_geocoding_cache_1 | True | location_query |

##### users

- **Rows**: 3
- **Columns**: 19
- **Indexes**: 7

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| user_id | INTEGER | False | NULL | True |
| username | TEXT | True | NULL | False |
| password_hash | TEXT | True | NULL | False |
| email | TEXT | True | NULL | False |
| role | TEXT | True | NULL | False |
| group_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_login | TIMESTAMP | False | NULL | False |
| account_status | TEXT | False | 'pending' | False |
| failed_login_attempts | INTEGER | False | 0 | False |
| reset_token | TEXT | False | NULL | False |
| reset_token_expiry | TIMESTAMP | False | NULL | False |
| email_verified | BOOLEAN | False | 0 | False |
| verification_token | TEXT | False | NULL | False |
| verification_token_expiry | TIMESTAMP | False | NULL | False |
| password_changed_at | TIMESTAMP | False | NULL | False |
| profile_picture | TEXT | False | NULL | False |
| full_name | TEXT | False | NULL | False |
| notification_preferences | TEXT | False | '{"email_alerts": true, "security_notifications": true}' | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_users_group_id | False | group_id |
| idx_users_account_status | False | account_status |
| idx_users_role | False | role |
| idx_users_email | False | email |
| idx_users_username | False | username |
| sqlite_autoindex_users_2 | True | email |
| sqlite_autoindex_users_1 | True | username |

##### permission_groups

- **Rows**: 4
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| group_id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_groups_name | False | name |
| sqlite_autoindex_permission_groups_1 | True | name |

##### group_permissions

- **Rows**: 60
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| group_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_group_permissions_function | False | function_name |
| idx_group_permissions_group_id | False | group_id |
| sqlite_autoindex_group_permissions_1 | True | group_id, function_name |

##### permission_overrides

- **Rows**: 17
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_overrides_function | False | function_name |
| idx_permission_overrides_user_id | False | user_id |
| sqlite_autoindex_permission_overrides_1 | True | user_id, function_name |

##### permission_audit_logs

- **Rows**: 27
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| admin_user_id | INTEGER | False | NULL | False |
| target_user_id | INTEGER | False | NULL | False |
| change_type | TEXT | True | NULL | False |
| entity_changed | TEXT | True | NULL | False |
| old_value | TEXT | False | NULL | False |
| new_value | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_audit_logs_change_type | False | change_type |
| idx_permission_audit_logs_timestamp | False | timestamp |
| idx_permission_audit_logs_target_user_id | False | target_user_id |
| idx_permission_audit_logs_admin_user_id | False | admin_user_id |

##### dashboard_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_dashboard_permissions_function | False | function_name |
| idx_dashboard_permissions_user_id | False | user_id |
| sqlite_autoindex_dashboard_permissions_1 | True | user_id, function_name |

##### category_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| category | TEXT | True | NULL | False |
| permission | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_category_permissions_category | False | category |
| idx_category_permissions_user_id | False | user_id |
| sqlite_autoindex_category_permissions_1 | True | user_id, category |

##### user_activity_logs

- **Rows**: 11
- **Columns**: 9
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| ip_address | TEXT | False | NULL | False |
| action_type | TEXT | True | NULL | False |
| details | TEXT | False | NULL | False |
| resource_type | TEXT | False | NULL | False |
| resource_id | TEXT | False | NULL | False |
| status | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_activity_logs_timestamp | False | timestamp |
| idx_user_activity_logs_action_type | False | action_type |
| idx_user_activity_logs_user_id | False | user_id |

##### user_sessions

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| session_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| session_token | TEXT | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |
| user_agent | TEXT | False | NULL | False |
| start_time | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| end_time | TIMESTAMP | False | NULL | False |
| is_active | BOOLEAN | False | 1 | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_sessions_device_fingerprint | False | device_fingerprint |
| idx_user_sessions_session_token | False | session_token |
| idx_user_sessions_user_id | False | user_id |
| sqlite_autoindex_user_sessions_1 | True | session_token |

##### forms

- **Rows**: 1
- **Columns**: 7
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| fields | TEXT | True | NULL | False |
| is_active | BOOLEAN | False | 1 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_forms_active | False | is_active |

##### form_submissions

- **Rows**: 19
- **Columns**: 8
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| form_id | INTEGER | True | NULL | False |
| user_id | INTEGER | False | NULL | False |
| pdf_document_id | INTEGER | True | NULL | False |
| submission_data | TEXT | True | NULL | False |
| ip_address | TEXT | False | NULL | False |
| user_agent | TEXT | False | NULL | False |
| submitted_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_form_submissions_submitted_at | False | submitted_at |
| idx_form_submissions_pdf_document_id | False | pdf_document_id |
| idx_form_submissions_form_id | False | form_id |

##### categories

- **Rows**: 3
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| form_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_categories_form_id | False | form_id |
| sqlite_autoindex_categories_1 | True | name |

##### pdf_documents

- **Rows**: 78
- **Columns**: 27
- **Indexes**: 6

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| filename | TEXT | True | NULL | False |
| original_filename | TEXT | True | NULL | False |
| category | TEXT | True | NULL | False |
| upload_date | TIMESTAMP | True | NULL | False |
| source_url_id | INTEGER | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| form_id | INTEGER | False | NULL | False |
| file_size | INTEGER | False | NULL | False |
| page_count | INTEGER | False | NULL | False |
| updated_at | TIMESTAMP | False | NULL | False |
| download_filename | TEXT | False | NULL | False |
| has_non_ocr_version | BOOLEAN | False | FALSE | False |
| conversion_settings | TEXT | False | NULL | False |
| published_year | INTEGER | False | NULL | False |
| published_month_start | INTEGER | False | NULL | False |
| published_month_end | INTEGER | False | NULL | False |
| published_month_range_str | TEXT | False | NULL | False |
| pdf_title | TEXT | False | NULL | False |
| pdf_author | TEXT | False | NULL | False |
| pdf_subject | TEXT | False | NULL | False |
| pdf_keywords | TEXT | False | NULL | False |
| pdf_creation_date | TIMESTAMP | False | NULL | False |
| pdf_modification_date | TIMESTAMP | False | NULL | False |
| pdf_version | TEXT | False | NULL | False |
| pdf_producer | TEXT | False | NULL | False |
| pdf_creator | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_pdf_documents_subject | False | pdf_subject |
| idx_pdf_documents_author | False | pdf_author |
| idx_pdf_documents_title | False | pdf_title |
| idx_pdf_documents_form_id | False | form_id |
| idx_pdf_documents_category | False | category |
| idx_pdf_documents_filename | False | filename |

---

### scraped_pages.db

- **Path**: `.\scraped_pages.db`
- **Size**: 0.01 MB (12288 bytes)
- **Tables**: 2
- **Page Count**: 3
- **Page Size**: 4096 bytes

#### Tables

##### scraped_pages

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| category | TEXT | True | NULL | False |
| url | TEXT | True | NULL | False |
| filename | TEXT | True | NULL | False |
| timestamp | DATETIME | False | CURRENT_TIMESTAMP | False |

##### sqlite_sequence

- **Rows**: 0
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| name |  | False | NULL | False |
| seq |  | False | NULL | False |

---

### user_management.db

- **Path**: `.\user_management.db`
- **Size**: 0.2 MB (204800 bytes)
- **Tables**: 11
- **Page Count**: 50
- **Page Size**: 4096 bytes

#### Tables

##### users

- **Rows**: 3
- **Columns**: 19
- **Indexes**: 7

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| user_id | INTEGER | False | NULL | True |
| username | TEXT | True | NULL | False |
| password_hash | TEXT | True | NULL | False |
| email | TEXT | True | NULL | False |
| role | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| last_login | TIMESTAMP | False | NULL | False |
| account_status | TEXT | False | 'pending' | False |
| failed_login_attempts | INTEGER | False | 0 | False |
| reset_token | TEXT | False | NULL | False |
| reset_token_expiry | TIMESTAMP | False | NULL | False |
| email_verified | BOOLEAN | False | 0 | False |
| verification_token | TEXT | False | NULL | False |
| verification_token_expiry | TIMESTAMP | False | NULL | False |
| password_changed_at | TIMESTAMP | False | NULL | False |
| profile_picture | TEXT | False | NULL | False |
| full_name | TEXT | False | NULL | False |
| notification_preferences | TEXT | False | '{"email_alerts": true, "security_notifications": true}' | False |
| group_id | INTEGER | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_users_group_id | False | group_id |
| idx_users_account_status | False | account_status |
| idx_users_role | False | role |
| idx_users_email | False | email |
| idx_users_username | False | username |
| sqlite_autoindex_users_2 | True | email |
| sqlite_autoindex_users_1 | True | username |

##### sqlite_sequence

- **Rows**: 6
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| name |  | False | NULL | False |
| seq |  | False | NULL | False |

##### category_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| category | TEXT | True | NULL | False |
| permission | TEXT | True | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_category_permissions_category | False | category |
| idx_category_permissions_user_id | False | user_id |
| sqlite_autoindex_category_permissions_1 | True | user_id, category |

##### user_activity_logs

- **Rows**: 100
- **Columns**: 9
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| ip_address | TEXT | False | NULL | False |
| action_type | TEXT | True | NULL | False |
| details | TEXT | False | NULL | False |
| resource_type | TEXT | False | NULL | False |
| resource_id | TEXT | False | NULL | False |
| status | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_activity_logs_timestamp | False | timestamp |
| idx_user_activity_logs_action_type | False | action_type |
| idx_user_activity_logs_user_id | False | user_id |

##### dashboard_permissions

- **Rows**: 0
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_dashboard_permissions_function | False | function_name |
| idx_dashboard_permissions_user_id | False | user_id |
| sqlite_autoindex_dashboard_permissions_1 | True | user_id, function_name |

##### permission_groups

- **Rows**: 4
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| group_id | INTEGER | False | NULL | True |
| name | TEXT | True | NULL | False |
| description | TEXT | False | NULL | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_groups_name | False | name |
| sqlite_autoindex_permission_groups_1 | True | name |

##### group_permissions

- **Rows**: 60
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| group_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_group_permissions_function | False | function_name |
| idx_group_permissions_group_id | False | group_id |
| sqlite_autoindex_group_permissions_1 | True | group_id, function_name |

##### permission_overrides

- **Rows**: 10
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| user_id | INTEGER | True | NULL | False |
| function_name | TEXT | True | NULL | False |
| enabled | BOOLEAN | True | 0 | False |
| created_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| updated_at | TIMESTAMP | False | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_overrides_function | False | function_name |
| idx_permission_overrides_user_id | False | user_id |
| sqlite_autoindex_permission_overrides_1 | True | user_id, function_name |

##### permission_audit_logs

- **Rows**: 26
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| log_id | INTEGER | False | NULL | True |
| timestamp | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| admin_user_id | INTEGER | False | NULL | False |
| target_user_id | INTEGER | False | NULL | False |
| change_type | TEXT | True | NULL | False |
| entity_changed | TEXT | True | NULL | False |
| old_value | TEXT | False | NULL | False |
| new_value | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_permission_audit_logs_change_type | False | change_type |
| idx_permission_audit_logs_timestamp | False | timestamp |
| idx_permission_audit_logs_target_user_id | False | target_user_id |
| idx_permission_audit_logs_admin_user_id | False | admin_user_id |

##### user_sessions

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| session_id | INTEGER | False | NULL | True |
| user_id | INTEGER | False | NULL | False |
| session_token | TEXT | False | NULL | False |
| device_fingerprint | TEXT | False | NULL | False |
| ip_address | TEXT | False | NULL | False |
| user_agent | TEXT | False | NULL | False |
| start_time | TIMESTAMP | False | CURRENT_TIMESTAMP | False |
| end_time | TIMESTAMP | False | NULL | False |
| is_active | BOOLEAN | False | 1 | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| idx_user_sessions_device_fingerprint | False | device_fingerprint |
| idx_user_sessions_session_token | False | session_token |
| idx_user_sessions_user_id | False | user_id |
| sqlite_autoindex_user_sessions_1 | True | session_token |

##### sqlite_stat1

- **Rows**: 22
- **Columns**: 3
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tbl |  | False | NULL | False |
| idx |  | False | NULL | False |
| stat |  | False | NULL | False |

---

### content_db.sqlite

- **Path**: `.\data\content_db.sqlite`
- **Size**: 0.0 MB (0 bytes)
- **Tables**: 0
- **Page Count**: 0
- **Page Size**: 4096 bytes

#### Tables

---

### erdb.db

- **Path**: `.\data\erdb.db`
- **Size**: 0.0 MB (0 bytes)
- **Tables**: 0
- **Page Count**: 0
- **Page Size**: 4096 bytes

#### Tables

---

### ITIS.sqlite

- **Path**: `.\data\itisSqlite060625\ITIS.sqlite`
- **Size**: 879.33 MB (922046464 bytes)
- **Tables**: 25
- **Page Count**: 900436
- **Page Size**: 1024 bytes

#### Tables

##### HierarchyToRank

- **Rows**: 0
- **Columns**: 12
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| kingdom_tsn | int(11) | False | NULL | False |
| level | int(11) | False | NULL | False |
| rank_id | smallint(6) | False | NULL | False |
| rank_name | char(15) | False | NULL | False |
| tsn | int(11) | False | NULL | False |
| parent_tsn | int(11) | False | NULL | False |
| scientific_name | varchar(163) | False | NULL | False |
| taxon_author | varchar(100) | False | NULL | False |
| credibility_rtng | varchar(40) | False | NULL | False |
| sort | varchar(400) | False | NULL | False |
| DirectChildrenCount | int(11) | True | NULL | False |
| synonyms | varchar(1000) | False | NULL | False |

##### change_comments

- **Rows**: 0
- **Columns**: 4
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| change_track_id | int(11) | True | NULL | True |
| chg_cmt_id | int(11) | True | NULL | True |
| change_detail | varchar(250) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| change_comments_change_comments_index | False | change_track_id, chg_cmt_id |
| sqlite_autoindex_change_comments_1 | True | change_track_id, chg_cmt_id |

##### change_operations

- **Rows**: 0
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| change_track_id | int(11) | True | NULL | True |
| chg_op_id | int(11) | True | NULL | True |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| change_operations_change_operations_index | False | change_track_id, chg_op_id |
| sqlite_autoindex_change_operations_1 | True | change_track_id, chg_op_id |

##### change_tracks

- **Rows**: 0
- **Columns**: 9
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| change_track_id | int(11) | True | NULL | True |
| old_tsn | int(11) | False | NULL | False |
| change_reason | varchar(40) | True | NULL | False |
| change_initiator | varchar(100) | True | NULL | False |
| change_reviewer | varchar(100) | True | NULL | False |
| change_certifier | varchar(100) | True | NULL | False |
| change_time_stamp | datetime | True | NULL | False |
| tsn | int(11) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| change_tracks_change_tracks_index | False | change_track_id |
| sqlite_autoindex_change_tracks_1 | True | change_track_id |

##### chg_operation_lkp

- **Rows**: 0
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| chg_op_id | int(11) | True | NULL | True |
| change_operation | varchar(25) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| chg_operation_lkp_chg_operation_lkp_index | False | chg_op_id |
| sqlite_autoindex_chg_operation_lkp_1 | True | chg_op_id |

##### comments

- **Rows**: 70,054
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| comment_id | int(11) | True | NULL | True |
| commentator | varchar(100) | False | NULL | False |
| comment_detail | TEXT | True | NULL | False |
| comment_time_stamp | datetime | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| comments_comments_index | False | comment_id |
| sqlite_autoindex_comments_1 | True | comment_id |

##### experts

- **Rows**: 197
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| expert_id_prefix | char(3) | True | NULL | True |
| expert_id | int(11) | True | NULL | True |
| expert | varchar(100) | True | NULL | False |
| exp_comment | varchar(500) | False | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| experts_experts_index | False | expert_id_prefix, expert_id |
| sqlite_autoindex_experts_1 | True | expert_id_prefix, expert_id |

##### geographic_div

- **Rows**: 459,772
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| geographic_value | varchar(45) | True | NULL | True |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| geographic_div_geographic_index | False | tsn, geographic_value |
| sqlite_autoindex_geographic_div_1 | True | tsn, geographic_value |

##### hierarchy

- **Rows**: 668,613
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| hierarchy_string | varchar(300) | True | NULL | True |
| TSN | int(11) | True | NULL | False |
| Parent_TSN | int(11) | False | NULL | False |
| level | int(11) | True | NULL | False |
| ChildrenCount | int(11) | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| hierarchy_hierarchy_string | False | hierarchy_string |
| sqlite_autoindex_hierarchy_1 | True | hierarchy_string |

##### jurisdiction

- **Rows**: 158,777
- **Columns**: 4
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| jurisdiction_value | varchar(30) | True | NULL | True |
| origin | varchar(19) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| jurisdiction_jurisdiction_index | False | tsn, jurisdiction_value |
| sqlite_autoindex_jurisdiction_1 | True | tsn, jurisdiction_value |

##### kingdoms

- **Rows**: 7
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| kingdom_id | int(11) | True | NULL | True |
| kingdom_name | char(10) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| kingdoms_kingdoms_index | False | kingdom_id, kingdom_name |
| sqlite_autoindex_kingdoms_1 | True | kingdom_id |

##### longnames

- **Rows**: 970,694
- **Columns**: 2
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| completename | varchar(164) | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| longnames_tsn | False | tsn, completename |
| sqlite_autoindex_longnames_1 | True | tsn |

##### nodc_ids

- **Rows**: 209,565
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| nodc_id | char(12) | True | NULL | True |
| update_date | date | True | NULL | False |
| tsn | int(11) | True | NULL | True |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| nodc_ids_nodc_index | False | nodc_id, tsn |
| sqlite_autoindex_nodc_ids_1 | True | nodc_id, tsn |

##### other_sources

- **Rows**: 1,035
- **Columns**: 8
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| source_id_prefix | char(3) | True | NULL | True |
| source_id | int(11) | True | NULL | True |
| source_type | char(10) | True | NULL | False |
| source | varchar(64) | True | NULL | False |
| version | char(10) | True | NULL | False |
| acquisition_date | date | True | NULL | False |
| source_comment | varchar(500) | False | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| other_sources_other_sources_index | False | source_id_prefix, source_id |
| sqlite_autoindex_other_sources_1 | True | source_id_prefix, source_id |

##### publications

- **Rows**: 27,454
- **Columns**: 14
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| pub_id_prefix | char(3) | True | NULL | True |
| publication_id | int(11) | True | NULL | True |
| reference_author | varchar(100) | True | NULL | False |
| title | varchar(255) | False | NULL | False |
| publication_name | varchar(255) | True | NULL | False |
| listed_pub_date | date | False | NULL | False |
| actual_pub_date | date | True | NULL | False |
| publisher | varchar(80) | False | NULL | False |
| pub_place | varchar(40) | False | NULL | False |
| isbn | varchar(16) | False | NULL | False |
| issn | varchar(16) | False | NULL | False |
| pages | varchar(15) | False | NULL | False |
| pub_comment | varchar(500) | False | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| publications_publications_index | False | pub_id_prefix, publication_id |
| sqlite_autoindex_publications_1 | True | pub_id_prefix, publication_id |

##### reference_links

- **Rows**: 1,900,706
- **Columns**: 8
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| doc_id_prefix | char(3) | True | NULL | True |
| documentation_id | int(11) | True | NULL | True |
| original_desc_ind | char(1) | False | NULL | False |
| init_itis_desc_ind | char(1) | False | NULL | False |
| change_track_id | int(11) | False | NULL | False |
| vernacular_name | varchar(80) | False | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| reference_links_reference_links_index | False | tsn, doc_id_prefix, documentation_id |
| sqlite_autoindex_reference_links_1 | True | tsn, doc_id_prefix, documentation_id |

##### reviews

- **Rows**: 0
- **Columns**: 7
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| review_start_date | date | True | NULL | True |
| review_end_date | date | False | NULL | False |
| review_reason | varchar(25) | True | NULL | False |
| reviewer | varchar(100) | True | NULL | False |
| review_comment | varchar(255) | False | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| reviews_reviews_index | False | tsn, review_start_date |
| sqlite_autoindex_reviews_1 | True | tsn, review_start_date |

##### strippedauthor

- **Rows**: 210,378
- **Columns**: 2
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| taxon_author_id | int(11) | True | NULL | True |
| shortauthor | varchar(100) | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| strippedauthor_taxon_author_id | False | taxon_author_id, shortauthor |
| sqlite_autoindex_strippedauthor_1 | True | taxon_author_id |

##### synonym_links

- **Rows**: 302,348
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| tsn_accepted | int(11) | True | NULL | True |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| synonym_links_synonym_links_index | False | tsn, tsn_accepted |
| sqlite_autoindex_synonym_links_1 | True | tsn, tsn_accepted |

##### taxon_authors_lkp

- **Rows**: 210,378
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| taxon_author_id | int(11) | True | NULL | True |
| taxon_author | varchar(100) | True | NULL | False |
| update_date | date | True | NULL | False |
| kingdom_id | smallint(6) | True | NULL | True |
| short_author | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| taxon_authors_lkp_taxon_authors_id_index | False | taxon_author_id, taxon_author, kingdom_id |
| sqlite_autoindex_taxon_authors_lkp_1 | True | taxon_author_id, kingdom_id |

##### taxon_unit_types

- **Rows**: 182
- **Columns**: 6
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| kingdom_id | int(11) | True | NULL | True |
| rank_id | smallint(6) | True | NULL | True |
| rank_name | char(15) | True | NULL | False |
| dir_parent_rank_id | smallint(6) | True | NULL | False |
| req_parent_rank_id | smallint(6) | True | NULL | False |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| taxon_unit_types_taxon_ut_index | False | kingdom_id, rank_id |
| sqlite_autoindex_taxon_unit_types_1 | True | kingdom_id, rank_id |

##### taxonomic_units

- **Rows**: 970,694
- **Columns**: 26
- **Indexes**: 5

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| unit_ind1 | char(1) | False | NULL | False |
| unit_name1 | char(35) | True | NULL | False |
| unit_ind2 | char(20) | False | NULL | False |
| unit_name2 | varchar(35) | False | NULL | False |
| unit_ind3 | varchar(20) | False | NULL | False |
| unit_name3 | varchar(35) | False | NULL | False |
| unit_ind4 | varchar(7) | False | NULL | False |
| unit_name4 | varchar(35) | False | NULL | False |
| unnamed_taxon_ind | char(1) | False | NULL | False |
| name_usage | varchar(12) | True | NULL | False |
| unaccept_reason | varchar(50) | False | NULL | False |
| credibility_rtng | varchar(40) | True | NULL | False |
| completeness_rtng | char(10) | False | NULL | False |
| currency_rating | char(7) | False | NULL | False |
| phylo_sort_seq | smallint(6) | False | NULL | False |
| initial_time_stamp | datetime | True | NULL | False |
| parent_tsn | int(11) | False | NULL | False |
| taxon_author_id | int(11) | False | NULL | False |
| hybrid_author_id | int(11) | False | NULL | False |
| kingdom_id | smallint(6) | True | NULL | False |
| rank_id | smallint(6) | True | NULL | False |
| update_date | date | True | NULL | False |
| uncertain_prnt_ind | char(3) | False | NULL | False |
| n_usage | TEXT | False | NULL | False |
| complete_name | tinytext | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| taxonomic_units_taxon_unit_index4 | False | tsn, taxon_author_id |
| taxonomic_units_taxon_unit_index3 | False | kingdom_id, rank_id |
| taxonomic_units_taxon_unit_index2 | False | tsn, unit_name1, name_usage |
| taxonomic_units_taxon_unit_index1 | False | tsn, parent_tsn |
| sqlite_autoindex_taxonomic_units_1 | True | tsn |

##### tu_comments_links

- **Rows**: 192,211
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| comment_id | int(11) | True | NULL | True |
| update_date | date | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| tu_comments_links_tu_comments_links_index | False | tsn, comment_id |
| sqlite_autoindex_tu_comments_links_1 | True | tsn, comment_id |

##### vern_ref_links

- **Rows**: 89,095
- **Columns**: 5
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| doc_id_prefix | char(3) | True | NULL | True |
| documentation_id | int(11) | True | NULL | True |
| update_date | date | True | NULL | False |
| vern_id | int(11) | True | NULL | True |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| vern_ref_links_vern_rl_index2 | False | tsn, vern_id |
| vern_ref_links_vern_rl_index1 | False | tsn, doc_id_prefix, documentation_id |
| sqlite_autoindex_vern_ref_links_1 | True | tsn, doc_id_prefix, documentation_id, vern_id |

##### vernaculars

- **Rows**: 155,767
- **Columns**: 6
- **Indexes**: 3

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tsn | int(11) | True | NULL | True |
| vernacular_name | varchar(80) | True | NULL | False |
| language | varchar(15) | True | NULL | False |
| approved_ind | char(1) | False | NULL | False |
| update_date | date | True | NULL | False |
| vern_id | int(11) | True | NULL | True |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| vernaculars_vernaculars_index2 | False | tsn, vern_id |
| vernaculars_vernaculars_index1 | False | tsn, vernacular_name, language |
| sqlite_autoindex_vernaculars_1 | True | tsn, vern_id |

---

### chroma.sqlite3

- **Path**: `.\data\unified_chroma\chroma.sqlite3`
- **Size**: 69.64 MB (73023488 bytes)
- **Tables**: 21
- **Page Count**: 17828
- **Page Size**: 4096 bytes

#### Tables

##### migrations

- **Rows**: 16
- **Columns**: 5
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| dir | TEXT | True | NULL | True |
| version | INTEGER | True | NULL | True |
| filename | TEXT | True | NULL | False |
| sql | TEXT | True | NULL | False |
| hash | TEXT | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_migrations_1 | True | dir, version |

##### acquire_write

- **Rows**: 52
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| lock_status | INTEGER | True | NULL | False |

##### collection_metadata

- **Rows**: 0
- **Columns**: 6
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| collection_id | TEXT | False | NULL | True |
| key | TEXT | True | NULL | True |
| str_value | TEXT | False | NULL | False |
| int_value | INTEGER | False | NULL | False |
| float_value | REAL | False | NULL | False |
| bool_value | INTEGER | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_collection_metadata_1 | True | collection_id, key |

##### segment_metadata

- **Rows**: 0
- **Columns**: 6
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| segment_id | TEXT | False | NULL | True |
| key | TEXT | True | NULL | True |
| str_value | TEXT | False | NULL | False |
| int_value | INTEGER | False | NULL | False |
| float_value | REAL | False | NULL | False |
| bool_value | INTEGER | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_segment_metadata_1 | True | segment_id, key |

##### tenants

- **Rows**: 1
- **Columns**: 1
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | TEXT | False | NULL | True |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_tenants_1 | True | id |

##### databases

- **Rows**: 1
- **Columns**: 3
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | TEXT | False | NULL | True |
| name | TEXT | True | NULL | False |
| tenant_id | TEXT | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_databases_2 | True | tenant_id, name |
| sqlite_autoindex_databases_1 | True | id |

##### collections

- **Rows**: 1
- **Columns**: 5
- **Indexes**: 2

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | TEXT | False | NULL | True |
| name | TEXT | True | NULL | False |
| dimension | INTEGER | False | NULL | False |
| database_id | TEXT | True | NULL | False |
| config_json_str | TEXT | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_collections_2 | True | name, database_id |
| sqlite_autoindex_collections_1 | True | id |

##### maintenance_log

- **Rows**: 0
- **Columns**: 3
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INT | False | NULL | True |
| timestamp | INT | True | NULL | False |
| operation | TEXT | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_maintenance_log_1 | True | id |

##### segments

- **Rows**: 2
- **Columns**: 4
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | TEXT | False | NULL | True |
| type | TEXT | True | NULL | False |
| scope | TEXT | True | NULL | False |
| collection | TEXT | True | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_segments_1 | True | id |

##### embeddings

- **Rows**: 5,581
- **Columns**: 5
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| segment_id | TEXT | True | NULL | False |
| embedding_id | TEXT | True | NULL | False |
| seq_id | BLOB | True | NULL | False |
| created_at | TIMESTAMP | True | CURRENT_TIMESTAMP | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_embeddings_1 | True | segment_id, embedding_id |

##### embedding_metadata

- **Rows**: 234,972
- **Columns**: 6
- **Indexes**: 4

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| key | TEXT | True | NULL | True |
| string_value | TEXT | False | NULL | False |
| int_value | INTEGER | False | NULL | False |
| float_value | REAL | False | NULL | False |
| bool_value | INTEGER | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| embedding_metadata_string_value | False | key, string_value |
| embedding_metadata_float_value | False | key, float_value |
| embedding_metadata_int_value | False | key, int_value |
| sqlite_autoindex_embedding_metadata_1 | True | id, key |

##### max_seq_id

- **Rows**: 2
- **Columns**: 2
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| segment_id | TEXT | False | NULL | True |
| seq_id | INTEGER | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_max_seq_id_1 | True | segment_id |

##### embedding_fulltext_search_data

- **Rows**: 4,400
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| block | BLOB | False | NULL | False |

##### embedding_fulltext_search_idx

- **Rows**: 3,733
- **Columns**: 3
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| segid |  | True | NULL | True |
| term |  | True | NULL | True |
| pgno |  | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_embedding_fulltext_search_idx_1 | True | segid, term |

##### embedding_fulltext_search_content

- **Rows**: 5,581
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| c0 |  | False | NULL | False |

##### embedding_fulltext_search_docsize

- **Rows**: 5,581
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| sz | BLOB | False | NULL | False |

##### embedding_fulltext_search_config

- **Rows**: 1
- **Columns**: 2
- **Indexes**: 1

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| k |  | True | NULL | True |
| v |  | False | NULL | False |

**Indexes:**

| Name | Unique | Columns |
|------|--------|---------|
| sqlite_autoindex_embedding_fulltext_search_config_1 | True | k |

##### embeddings_queue

- **Rows**: 821
- **Columns**: 8
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| seq_id | INTEGER | False | NULL | True |
| created_at | TIMESTAMP | True | CURRENT_TIMESTAMP | False |
| operation | INTEGER | True | NULL | False |
| topic | TEXT | True | NULL | False |
| id | TEXT | True | NULL | False |
| vector | BLOB | False | NULL | False |
| encoding | TEXT | False | NULL | False |
| metadata | TEXT | False | NULL | False |

##### embeddings_queue_config

- **Rows**: 1
- **Columns**: 2
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| id | INTEGER | False | NULL | True |
| config_json_str | TEXT | False | NULL | False |

##### sqlite_stat1

- **Rows**: 21
- **Columns**: 3
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| tbl |  | False | NULL | False |
| idx |  | False | NULL | False |
| stat |  | False | NULL | False |

##### embedding_fulltext_search

- **Rows**: 5,581
- **Columns**: 1
- **Indexes**: 0

**Columns:**

| Name | Type | Not Null | Default | Primary Key |
|------|------|----------|---------|-------------|
| string_value |  | False | NULL | False |

---

### chat_history.db

- **Path**: `.\instance\chat_history.db`
- **Size**: 0.0 MB (0 bytes)
- **Tables**: 0
- **Page Count**: 0
- **Page Size**: 4096 bytes

#### Tables

---

