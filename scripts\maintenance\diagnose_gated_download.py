#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic script for gated download form submission issues
"""

import os
import sqlite3
import json
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database path
DB_PATH = os.getenv("DB_PATH", "./erdb_main.db")

def check_database_structure():
    """Check if the forms system tables exist and are properly configured."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("🔍 CHECKING DATABASE STRUCTURE")
        print("=" * 50)
        
        # Check if forms table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='forms'")
        forms_exists = cursor.fetchone() is not None
        print(f"✅ Forms table exists: {forms_exists}")
        
        # Check if form_submissions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='form_submissions'")
        submissions_exists = cursor.fetchone() is not None
        print(f"✅ Form submissions table exists: {submissions_exists}")
        
        # Check if pdf_documents table has form_id column
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = [column[1] for column in cursor.fetchall()]
        form_id_exists = 'form_id' in columns
        print(f"✅ PDF documents table has form_id column: {form_id_exists}")
        
        if not forms_exists:
            print("❌ Forms table missing - need to run migration")
            return False
            
        if not submissions_exists:
            print("❌ Form submissions table missing - need to run migration")
            return False
            
        if not form_id_exists:
            print("❌ form_id column missing from pdf_documents - need to run migration")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking database structure: {e}")
        return False
    finally:
        if conn:
            conn.close()

def check_forms_data():
    """Check if there are any forms in the database."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 CHECKING FORMS DATA")
        print("=" * 50)
        
        cursor.execute("SELECT id, name, is_active FROM forms")
        forms = cursor.fetchall()
        
        if not forms:
            print("❌ No forms found in database")
            return False
            
        print(f"✅ Found {len(forms)} forms:")
        for form_id, name, is_active in forms:
            print(f"   - ID: {form_id}, Name: {name}, Active: {is_active}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking forms data: {e}")
        return False
    finally:
        if conn:
            conn.close()

def check_pdf_documents_with_forms():
    """Check if there are any PDF documents with form_id set."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 CHECKING PDF DOCUMENTS WITH FORMS")
        print("=" * 50)
        
        cursor.execute("""
            SELECT id, filename, original_filename, category, form_id 
            FROM pdf_documents 
            WHERE form_id IS NOT NULL
        """)
        gated_pdfs = cursor.fetchall()
        
        if not gated_pdfs:
            print("❌ No PDF documents with form_id found")
            return False
            
        print(f"✅ Found {len(gated_pdfs)} gated PDFs:")
        for pdf_id, filename, original_filename, category, form_id in gated_pdfs:
            print(f"   - ID: {pdf_id}, Filename: {filename}, Category: {category}, Form ID: {form_id}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking PDF documents: {e}")
        return False
    finally:
        if conn:
            conn.close()

def check_form_submissions():
    """Check if there are any form submissions in the database."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 CHECKING FORM SUBMISSIONS")
        print("=" * 50)
        
        cursor.execute("""
            SELECT fs.id, fs.form_id, fs.pdf_document_id, fs.submitted_at,
                   f.name as form_name, pd.original_filename as pdf_filename
            FROM form_submissions fs
            JOIN forms f ON fs.form_id = f.id
            JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
            ORDER BY fs.submitted_at DESC
            LIMIT 10
        """)
        submissions = cursor.fetchall()
        
        if not submissions:
            print("❌ No form submissions found")
            return False
            
        print(f"✅ Found {len(submissions)} form submissions:")
        for sub_id, form_id, pdf_id, submitted_at, form_name, pdf_filename in submissions:
            print(f"   - ID: {sub_id}, Form: {form_name}, PDF: {pdf_filename}, Submitted: {submitted_at}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking form submissions: {e}")
        return False
    finally:
        if conn:
            conn.close()

def check_recent_activity():
    """Check for recent gated download activity."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 CHECKING RECENT ACTIVITY")
        print("=" * 50)
        
        # Check for recent form submissions (last 7 days)
        cursor.execute("""
            SELECT COUNT(*) FROM form_submissions 
            WHERE submitted_at >= datetime('now', '-7 days')
        """)
        recent_submissions = cursor.fetchone()[0]
        print(f"✅ Recent submissions (last 7 days): {recent_submissions}")
        
        # Check for gated PDFs accessed recently
        cursor.execute("""
            SELECT COUNT(*) FROM pdf_documents 
            WHERE form_id IS NOT NULL AND updated_at >= datetime('now', '-7 days')
        """)
        recent_gated_pdfs = cursor.fetchone()[0]
        print(f"✅ Recent gated PDFs (last 7 days): {recent_gated_pdfs}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking recent activity: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_form_submission_creation():
    """Test creating a form submission to see if the issue is in the creation process."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        print("\n🔍 TESTING FORM SUBMISSION CREATION")
        print("=" * 50)
        
        # Get the first form
        cursor.execute("SELECT id FROM forms LIMIT 1")
        form_result = cursor.fetchone()
        
        if not form_result:
            print("❌ No forms available for testing")
            return False
            
        form_id = form_result[0]
        
        # Get the first PDF with form_id
        cursor.execute("SELECT id FROM pdf_documents WHERE form_id IS NOT NULL LIMIT 1")
        pdf_result = cursor.fetchone()
        
        if not pdf_result:
            print("❌ No gated PDFs available for testing")
            return False
            
        pdf_id = pdf_result[0]
        
        # Test submission data
        test_submission_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'organization': 'Test Organization'
        }
        
        # Try to create a test submission
        cursor.execute('''
            INSERT INTO form_submissions (form_id, pdf_document_id, submission_data, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ''', (form_id, pdf_id, json.dumps(test_submission_data), '127.0.0.1', 'Test Agent'))
        
        submission_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Successfully created test submission with ID: {submission_id}")
        
        # Clean up test submission
        cursor.execute("DELETE FROM form_submissions WHERE id = ?", (submission_id,))
        conn.commit()
        print("✅ Cleaned up test submission")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing form submission creation: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """Run all diagnostic checks."""
    print("🚀 GATED DOWNLOAD DIAGNOSTIC TOOL")
    print("=" * 60)
    print(f"Database: {DB_PATH}")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 60)
    
    # Run all checks
    checks = [
        ("Database Structure", check_database_structure),
        ("Forms Data", check_forms_data),
        ("PDF Documents with Forms", check_pdf_documents_with_forms),
        ("Form Submissions", check_form_submissions),
        ("Recent Activity", check_recent_activity),
        ("Form Submission Creation", test_form_submission_creation)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! The gated download system should be working correctly.")
    else:
        print("⚠️  Some checks failed. Please review the issues above and run the appropriate fixes.")
        
        if not results[0][1]:  # Database structure failed
            print("\n🔧 RECOMMENDED FIXES:")
            print("1. Run the forms system migration: python scripts/migrations/add_forms_system.py")
            print("2. Restart the application")
        elif not results[1][1]:  # Forms data failed
            print("\n🔧 RECOMMENDED FIXES:")
            print("1. Create default forms using the admin interface")
            print("2. Or run the migration script to create default forms")
        elif not results[2][1]:  # No gated PDFs
            print("\n🔧 RECOMMENDED FIXES:")
            print("1. Upload some PDFs with gated download forms")
            print("2. Or check if existing PDFs need form_id assignment")

if __name__ == "__main__":
    main() 