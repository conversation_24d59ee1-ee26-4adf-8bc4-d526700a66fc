{% extends "admin_base.html" %}

{% block title %}Form Submissions{% endblock %}

{% block content %}
<div class="container-fluid pt-4 px-4">
    <div class="row g-4">
        <div class="col-12">
            <div class="bg-light rounded h-100 p-4">
                <h6 class="mb-4">Form Submissions</h6>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Form Name</th>
                                <th scope="col">PDF Filename</th>
                                <th scope="col">Email</th>
                                <th scope="col">Full Name</th>
                                <th scope="col">Organization</th>
                                <th scope="col">Purpose</th>
                                <th scope="col">IP Address</th>
                                <th scope="col">Submitted At</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for submission in submissions %}
                            <tr>
                                <th scope="row">{{ loop.index }}</th>
                                <td>{{ submission.form_name }}</td>
                                <td>{{ submission.pdf_filename }}</td>
                                <td>
                                    {% if submission.submission_data.email %}
                                        <a href="mailto:{{ submission.submission_data.email }}" class="text-primary">
                                            {{ submission.submission_data.email }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if submission.submission_data.full_name %}
                                        {{ submission.submission_data.full_name }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if submission.submission_data.organization %}
                                        {{ submission.submission_data.organization }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if submission.submission_data.purpose %}
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ submission.submission_data.purpose }}">
                                            {{ submission.submission_data.purpose }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>{{ submission.ip_address }}</td>
                                <td>{{ submission.submitted_at }}</td>
                                <td>
                                    <form action="{{ url_for('delete_submission', submission_id=submission.id) }}" method="POST" style="display:inline;">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this submission?');">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="10" class="text-center">No submissions found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 