#!/usr/bin/env python3
"""
Debug script to check module loading and paths.
"""

import os
import sys

def debug_module():
    """Debug module loading and paths."""
    
    print("Debugging module loading...")
    
    # Check current working directory
    print(f"Current working directory: {os.getcwd()}")
    
    # Check Python path
    print(f"Python path:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    # Check if the module file exists
    module_path = "app/utils/db_connection.py"
    if os.path.exists(module_path):
        print(f"✅ Module file exists: {module_path}")
        
        # Read the first few lines to check the USER_DB_PATH
        with open(module_path, 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:30]):
                if 'USER_DB_PATH' in line:
                    print(f"  Line {i+1}: {line.strip()}")
    else:
        print(f"❌ Module file not found: {module_path}")
    
    # Try to import the module
    try:
        import app.utils.db_connection as db
        print(f"✅ Module imported successfully")
        print(f"  USER_DB_PATH: {db.USER_DB_PATH}")
        print(f"  Module file: {db.__file__}")
    except Exception as e:
        print(f"❌ Module import failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_module()
