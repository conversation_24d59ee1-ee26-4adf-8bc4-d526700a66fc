"""
Database utility functions for the user management system.

This module provides utilities for database connections, transactions,
and common database operations.
"""

import os
import sqlite3
import logging
import threading
import time
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Tuple, Union, Generator

# Import performance monitoring
from app.utils.performance_monitor import DatabaseMetric, get_performance_monitor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database paths
USER_DB_PATH = os.getenv("USER_DB_PATH", "./user_management.db")
MAIN_DB_PATH = os.getenv("DB_PATH", "./erdb_main.db")

# Connection pool settings
MAX_CONNECTIONS = 5
CONNECTION_TIMEOUT = 5  # seconds
_connection_pools = {
    USER_DB_PATH: [],
    MAIN_DB_PATH: []
}
_pool_locks = {
    USER_DB_PATH: threading.Lock(),
    MAIN_DB_PATH: threading.Lock()
}

# SQLite performance settings
SQLITE_OPTIMIZATIONS = {
    'journal_mode': 'WAL',  # Write-Ahead Logging for better concurrency
    'synchronous': 'NORMAL',  # Balance between safety and performance
    'cache_size': 10000,  # 10MB cache
    'temp_store': 'MEMORY',  # Store temp tables in memory
    'mmap_size': 268435456,  # 256MB memory mapping
    'foreign_keys': 'ON',  # Enable foreign key constraints
    'busy_timeout': 30000,  # 30 second timeout for busy database
}


def dict_factory(cursor: sqlite3.Cursor, row: tuple) -> dict:
    """Convert database row objects to dictionaries."""
    return {col[0]: row[idx] for idx, col in enumerate(cursor.description)}


def optimize_connection(connection: sqlite3.Connection) -> None:
    """Apply SQLite performance optimizations to a connection."""
    try:
        for pragma, value in SQLITE_OPTIMIZATIONS.items():
            if pragma == 'foreign_keys':
                connection.execute(f"PRAGMA {pragma} = {value}")
            else:
                connection.execute(f"PRAGMA {pragma} = '{value}'")

        # Additional corruption prevention settings
        connection.execute("PRAGMA wal_autocheckpoint = 1000")
        connection.execute("PRAGMA checkpoint_fullfsync = ON")

    except sqlite3.Error as e:
        logger.warning(f"Failed to apply SQLite optimizations: {str(e)}")


def get_connection(db_path: str = USER_DB_PATH) -> sqlite3.Connection:
    """
    Get a database connection from the pool or create a new one.

    Args:
        db_path: Path to the SQLite database file

    Returns:
        A SQLite connection object
    """
    with _pool_locks.get(db_path, threading.Lock()):
        # Try to get a connection from the pool
        pool = _connection_pools.get(db_path, [])

        if pool:
            connection = pool.pop(0)
            try:
                # Test if the connection is still valid
                connection.execute("SELECT 1")
                return connection
            except sqlite3.Error:
                # Connection is no longer valid, create a new one
                logger.debug(f"Discarding invalid connection for {db_path}")
                try:
                    connection.close()
                except:
                    pass

        # Create a new connection
        start_time = time.time()
        while True:
            try:
                connection = sqlite3.connect(db_path)
                connection.row_factory = dict_factory
                
                # Apply performance optimizations
                optimize_connection(connection)
                
                return connection
            except sqlite3.Error as e:
                if time.time() - start_time > CONNECTION_TIMEOUT:
                    logger.error(f"Failed to connect to database {db_path}: {str(e)}")
                    raise
                time.sleep(0.1)


def return_connection(connection: sqlite3.Connection, db_path: str = USER_DB_PATH) -> None:
    """
    Return a connection to the pool.

    Args:
        connection: The SQLite connection to return
        db_path: Path to the SQLite database file
    """
    with _pool_locks.get(db_path, threading.Lock()):
        pool = _connection_pools.get(db_path, [])
        if len(pool) < MAX_CONNECTIONS:
            pool.append(connection)
        else:
            connection.close()


@contextmanager
def db_connection(db_path: str = USER_DB_PATH) -> Generator[sqlite3.Connection, None, None]:
    """
    Context manager for database connections.

    Args:
        db_path: Path to the SQLite database file

    Yields:
        A SQLite connection object
    """
    connection = None
    try:
        connection = get_connection(db_path)
        yield connection
    finally:
        if connection:
            return_connection(connection, db_path)


@contextmanager
def db_transaction(db_path: str = USER_DB_PATH) -> Generator[sqlite3.Connection, None, None]:
    """
    Context manager for database transactions with improved error handling.

    Args:
        db_path: Path to the SQLite database file

    Yields:
        A SQLite connection object with an active transaction
    """
    connection = None
    try:
        connection = get_connection(db_path)

        # Check database integrity before starting transaction
        try:
            connection.execute("PRAGMA quick_check(1)")
        except sqlite3.DatabaseError as e:
            logger.error(f"Database integrity issue detected: {str(e)}")
            raise sqlite3.DatabaseError(f"Database corruption detected: {str(e)}")

        connection.execute("BEGIN IMMEDIATE")  # Use IMMEDIATE to prevent deadlocks
        yield connection
        connection.commit()

    except sqlite3.DatabaseError as e:
        if connection:
            try:
                connection.rollback()
            except:
                pass
        logger.error(f"Database error in transaction: {str(e)}")
        raise
    except Exception as e:
        if connection:
            try:
                connection.rollback()
            except:
                pass
        logger.error(f"Transaction failed: {str(e)}")
        raise
    finally:
        if connection:
            return_connection(connection, db_path)


def execute_query(query: str, params: tuple = (), db_path: str = USER_DB_PATH) -> List[Dict[str, Any]]:
    """
    Execute a SELECT query and return the results.

    Args:
        query: SQL query string
        params: Query parameters
        db_path: Path to the SQLite database file

    Returns:
        List of dictionaries representing the query results
    """
    start_time = time.time()
    error = None
    rows_affected = 0

    try:
        with db_connection(db_path) as connection:
            cursor = connection.cursor()
            cursor.execute(query, params)
            result = cursor.fetchall()
            rows_affected = len(result)
            return result
    except Exception as e:
        error = str(e)
        raise
    finally:
        # Record performance metric
        execution_time = time.time() - start_time

        # Create query hash for tracking
        import hashlib
        query_hash = hashlib.md5(f"{query}_{params}".encode()).hexdigest()[:16]

        metric = DatabaseMetric(
            operation_type="SELECT",
            table_name=_extract_table_from_query(query),
            execution_time=execution_time,
            rows_affected=rows_affected,
            query_hash=query_hash,
            timestamp=time.time(),
            database_name=os.path.basename(db_path),
            error=error
        )

        get_performance_monitor().add_db_metric(metric)

        if execution_time > 1.0:  # Log slow queries
            logger.warning(f"Slow query detected: {execution_time:.3f}s - {query[:100]}...")


def execute_update(query: str, params: tuple = (), db_path: str = USER_DB_PATH) -> int:
    """
    Execute an UPDATE, INSERT, or DELETE query and return the number of affected rows.

    Args:
        query: SQL query string
        params: Query parameters
        db_path: Path to the SQLite database file

    Returns:
        Number of affected rows
    """
    start_time = time.time()
    error = None
    rows_affected = 0

    try:
        with db_transaction(db_path) as connection:
            cursor = connection.cursor()
            cursor.execute(query, params)
            rows_affected = cursor.rowcount
            return rows_affected
    except Exception as e:
        error = str(e)
        raise
    finally:
        # Record performance metric
        execution_time = time.time() - start_time

        # Determine operation type
        operation_type = _extract_operation_from_query(query)

        # Create query hash for tracking
        import hashlib
        query_hash = hashlib.md5(f"{query}_{params}".encode()).hexdigest()[:16]

        metric = DatabaseMetric(
            operation_type=operation_type,
            table_name=_extract_table_from_query(query),
            execution_time=execution_time,
            rows_affected=rows_affected,
            query_hash=query_hash,
            timestamp=time.time(),
            database_name=os.path.basename(db_path),
            error=error
        )

        get_performance_monitor().add_db_metric(metric)

        if execution_time > 0.5:  # Log slow updates
            logger.warning(f"Slow {operation_type} query: {execution_time:.3f}s - {query[:100]}...")


def execute_insert(query: str, params: tuple = (), db_path: str = USER_DB_PATH) -> int:
    """
    Execute an INSERT query and return the ID of the inserted row.

    Args:
        query: SQL query string
        params: Query parameters
        db_path: Path to the SQLite database file

    Returns:
        ID of the inserted row
    """
    with db_transaction(db_path) as connection:
        cursor = connection.cursor()
        cursor.execute(query, params)
        return cursor.lastrowid


def get_by_id(table: str, id_column: str, id_value: int, db_path: str = USER_DB_PATH) -> Optional[Dict[str, Any]]:
    """
    Get a record by its ID.

    Args:
        table: Table name
        id_column: Name of the ID column
        id_value: ID value to look up
        db_path: Path to the SQLite database file

    Returns:
        Dictionary representing the record, or None if not found
    """
    query = f"SELECT * FROM {table} WHERE {id_column} = ?"
    results = execute_query(query, (id_value,), db_path)
    return results[0] if results else None


def initialize_database(db_path: str = USER_DB_PATH) -> bool:
    """
    Initialize the database with all required tables.

    Args:
        db_path: Path to the SQLite database file

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create user management tables
        with db_transaction(db_path) as connection:
            cursor = connection.cursor()

            # Create users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    role TEXT NOT NULL CHECK(role IN ('admin', 'editor', 'viewer')),
                    group_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    account_status TEXT DEFAULT 'pending' CHECK(account_status IN ('active', 'pending', 'locked', 'disabled')),
                    failed_login_attempts INTEGER DEFAULT 0,
                    reset_token TEXT,
                    reset_token_expiry TIMESTAMP,
                    email_verified BOOLEAN DEFAULT 0,
                    verification_token TEXT,
                    verification_token_expiry TIMESTAMP,
                    password_changed_at TIMESTAMP,
                    profile_picture TEXT,
                    full_name TEXT,
                    notification_preferences TEXT DEFAULT '{"email_alerts": true, "security_notifications": true}',
                    FOREIGN KEY (group_id) REFERENCES permission_groups(group_id) ON DELETE SET NULL
                )
            ''')

            # Create permission groups table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS permission_groups (
                    group_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create group permissions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_id INTEGER NOT NULL,
                    function_name TEXT NOT NULL,
                    enabled BOOLEAN NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (group_id) REFERENCES permission_groups(group_id) ON DELETE CASCADE,
                    UNIQUE(group_id, function_name)
                )
            ''')

            # Create permission overrides table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS permission_overrides (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    function_name TEXT NOT NULL,
                    enabled BOOLEAN NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    UNIQUE(user_id, function_name)
                )
            ''')

            # Create permission audit logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS permission_audit_logs (
                    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    admin_user_id INTEGER,
                    target_user_id INTEGER,
                    change_type TEXT NOT NULL,
                    entity_changed TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    ip_address TEXT,
                    FOREIGN KEY (admin_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
                    FOREIGN KEY (target_user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ''')

            # Create dashboard permissions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dashboard_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    function_name TEXT NOT NULL,
                    enabled BOOLEAN NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    UNIQUE(user_id, function_name)
                )
            ''')

            # Create category permissions table (legacy)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS category_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    category TEXT NOT NULL,
                    permission TEXT NOT NULL CHECK(permission IN ('read', 'write', 'admin')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    UNIQUE(user_id, category)
                )
            ''')

            # Create user activity logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_activity_logs (
                    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    action_type TEXT NOT NULL,
                    details TEXT,
                    resource_type TEXT,
                    resource_id TEXT,
                    status TEXT CHECK(status IN ('success', 'failure', 'warning', 'info')),
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
                )
            ''')

            # Create user sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    session_token TEXT UNIQUE,
                    device_fingerprint TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
                )
            ''')

            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_account_status ON users(account_status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_group_id ON users(group_id)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_groups_name ON permission_groups(name)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_group_permissions_group_id ON group_permissions(group_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_group_permissions_function ON group_permissions(function_name)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_overrides_user_id ON permission_overrides(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_overrides_function ON permission_overrides(function_name)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_logs_admin_user_id ON permission_audit_logs(admin_user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_logs_target_user_id ON permission_audit_logs(target_user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_logs_timestamp ON permission_audit_logs(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_logs_change_type ON permission_audit_logs(change_type)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_dashboard_permissions_user_id ON dashboard_permissions(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_dashboard_permissions_function ON dashboard_permissions(function_name)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_category_permissions_user_id ON category_permissions(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_category_permissions_category ON category_permissions(category)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action_type ON user_activity_logs(action_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_activity_logs_timestamp ON user_activity_logs(timestamp)")

            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sessions_device_fingerprint ON user_sessions(device_fingerprint)")

        logger.info(f"Database initialized successfully at {db_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        return False

def _extract_table_from_query(query: str) -> str:
    """Extract table name from SQL query."""
    import re

    query_upper = query.upper().strip()

    # Common table names in ERDB
    tables = ['USERS', 'CHAT_HISTORY', 'PDF_DOCUMENTS', 'SOURCE_URLS',
              'URL_CONTENT', 'COVER_IMAGES', 'EXTRACTED_LOCATIONS',
              'USER_SESSIONS', 'CHAT_ANALYTICS', 'CONTENT_SOURCES']

    for table in tables:
        if table in query_upper:
            return table.lower()

    # Try to extract table name using regex patterns
    patterns = [
        r'FROM\s+(\w+)',
        r'UPDATE\s+(\w+)',
        r'INSERT\s+INTO\s+(\w+)',
        r'DELETE\s+FROM\s+(\w+)'
    ]

    for pattern in patterns:
        match = re.search(pattern, query_upper)
        if match:
            return match.group(1).lower()

    return 'unknown'

def _extract_operation_from_query(query: str) -> str:
    """Extract operation type from SQL query."""
    query_upper = query.upper().strip()

    if query_upper.startswith('SELECT'):
        return 'SELECT'
    elif query_upper.startswith('INSERT'):
        return 'INSERT'
    elif query_upper.startswith('UPDATE'):
        return 'UPDATE'
    elif query_upper.startswith('DELETE'):
        return 'DELETE'
    elif query_upper.startswith('CREATE'):
        return 'CREATE'
    elif query_upper.startswith('DROP'):
        return 'DROP'
    elif query_upper.startswith('ALTER'):
        return 'ALTER'

    return 'UNKNOWN'
