#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Analysis Script
Analyzes all databases in the ERDB system and generates comprehensive documentation.
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    def __init__(self):
        self.analysis_results = {
            "analysis_timestamp": datetime.now().isoformat(),
            "databases": {},
            "summary": {}
        }
        
    def analyze_database(self, db_path: str) -> Dict[str, Any]:
        """Analyze a single database and return its structure."""
        try:
            if not os.path.exists(db_path):
                return {"error": f"Database file not found: {db_path}"}
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get database info
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            file_size = os.path.getsize(db_path)
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            db_info = {
                "path": db_path,
                "size_bytes": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2),
                "page_count": page_count,
                "page_size": page_size,
                "tables": {}
            }
            
            # Analyze each table
            for table in tables:
                table_name = table[0]
                table_info = self.analyze_table(cursor, table_name)
                db_info["tables"][table_name] = table_info
            
            conn.close()
            return db_info
            
        except Exception as e:
            logger.error(f"Error analyzing database {db_path}: {str(e)}")
            return {"error": str(e)}
    
    def analyze_table(self, cursor: sqlite3.Cursor, table_name: str) -> Dict[str, Any]:
        """Analyze a single table and return its structure."""
        try:
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            # Get indexes
            cursor.execute(f"PRAGMA index_list({table_name})")
            indexes = cursor.fetchall()
            
            table_info = {
                "row_count": row_count,
                "columns": [],
                "indexes": []
            }
            
            # Analyze columns
            for col in columns:
                column_info = {
                    "name": col[1],
                    "type": col[2],
                    "not_null": bool(col[3]),
                    "default": col[4],
                    "primary_key": bool(col[5])
                }
                table_info["columns"].append(column_info)
            
            # Analyze indexes
            for idx in indexes:
                index_name = idx[1]
                cursor.execute(f"PRAGMA index_info({index_name})")
                index_columns = cursor.fetchall()
                
                index_info = {
                    "name": index_name,
                    "unique": bool(idx[2]),
                    "columns": [col[2] for col in index_columns]
                }
                table_info["indexes"].append(index_info)
            
            return table_info
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {str(e)}")
            return {"error": str(e)}
    
    def find_databases(self) -> List[str]:
        """Find all database files in the project."""
        databases = []
        
        # Common database extensions
        extensions = ['.db', '.sqlite', '.sqlite3']
        
        # Search in current directory and subdirectories
        for root, dirs, files in os.walk('.'):
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    db_path = os.path.join(root, file)
                    # Skip backup directories
                    if 'backup' not in db_path.lower() and 'temp' not in db_path.lower():
                        databases.append(db_path)
        
        return databases
    
    def analyze_all_databases(self):
        """Analyze all databases found in the project."""
        databases = self.find_databases()
        logger.info(f"Found {len(databases)} databases to analyze")
        
        for db_path in databases:
            logger.info(f"Analyzing database: {db_path}")
            db_info = self.analyze_database(db_path)
            self.analysis_results["databases"][db_path] = db_info
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate a summary of the analysis."""
        total_databases = len(self.analysis_results["databases"])
        total_size_mb = 0
        total_tables = 0
        
        for db_path, db_info in self.analysis_results["databases"].items():
            if "error" not in db_info:
                total_size_mb += db_info.get("size_mb", 0)
                total_tables += len(db_info.get("tables", {}))
        
        self.analysis_results["summary"] = {
            "total_databases": total_databases,
            "total_size_mb": round(total_size_mb, 2),
            "total_tables": total_tables,
            "database_types": {
                "sqlite": total_databases,
                "vector_databases": len([db for db in self.analysis_results["databases"] if "chroma" in db.lower()])
            }
        }
    
    def save_analysis(self, output_file: str = "database_analysis.json"):
        """Save the analysis results to a JSON file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        logger.info(f"Analysis saved to {output_file}")
    
    def generate_markdown_documentation(self, output_file: str = "DATABASE_DOCUMENTATION.md"):
        """Generate comprehensive markdown documentation."""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# ERDB System Database Documentation\n\n")
            f.write(f"*Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
            
            # Summary
            f.write("## Executive Summary\n\n")
            summary = self.analysis_results["summary"]
            f.write(f"- **Total Databases**: {summary['total_databases']}\n")
            f.write(f"- **Total Size**: {summary['total_size_mb']} MB\n")
            f.write(f"- **Total Tables**: {summary['total_tables']}\n")
            f.write(f"- **Vector Databases**: {summary['database_types']['vector_databases']}\n\n")
            
            # Database Details
            f.write("## Database Details\n\n")
            
            for db_path, db_info in self.analysis_results["databases"].items():
                if "error" in db_info:
                    f.write(f"### {os.path.basename(db_path)}\n\n")
                    f.write(f"**Error**: {db_info['error']}\n\n")
                    continue
                
                f.write(f"### {os.path.basename(db_path)}\n\n")
                f.write(f"- **Path**: `{db_path}`\n")
                f.write(f"- **Size**: {db_info['size_mb']} MB ({db_info['size_bytes']} bytes)\n")
                f.write(f"- **Tables**: {len(db_info['tables'])}\n")
                f.write(f"- **Page Count**: {db_info['page_count']}\n")
                f.write(f"- **Page Size**: {db_info['page_size']} bytes\n\n")
                
                # Tables
                f.write("#### Tables\n\n")
                for table_name, table_info in db_info['tables'].items():
                    if "error" in table_info:
                        f.write(f"##### {table_name}\n\n")
                        f.write(f"**Error**: {table_info['error']}\n\n")
                        continue
                    
                    f.write(f"##### {table_name}\n\n")
                    f.write(f"- **Rows**: {table_info['row_count']:,}\n")
                    f.write(f"- **Columns**: {len(table_info['columns'])}\n")
                    f.write(f"- **Indexes**: {len(table_info['indexes'])}\n\n")
                    
                    # Columns
                    f.write("**Columns:**\n\n")
                    f.write("| Name | Type | Not Null | Default | Primary Key |\n")
                    f.write("|------|------|----------|---------|-------------|\n")
                    for col in table_info['columns']:
                        f.write(f"| {col['name']} | {col['type']} | {col['not_null']} | {col['default'] or 'NULL'} | {col['primary_key']} |\n")
                    f.write("\n")
                    
                    # Indexes
                    if table_info['indexes']:
                        f.write("**Indexes:**\n\n")
                        f.write("| Name | Unique | Columns |\n")
                        f.write("|------|--------|---------|\n")
                        for idx in table_info['indexes']:
                            f.write(f"| {idx['name']} | {idx['unique']} | {', '.join(idx['columns'])} |\n")
                        f.write("\n")
                
                f.write("---\n\n")
        
        logger.info(f"Documentation saved to {output_file}")

def main():
    """Main function to run the database analysis."""
    analyzer = DatabaseAnalyzer()
    analyzer.analyze_all_databases()
    analyzer.save_analysis()
    analyzer.generate_markdown_documentation()
    
    print(f"\nAnalysis complete!")
    print(f"Found {analyzer.analysis_results['summary']['total_databases']} databases")
    print(f"Total size: {analyzer.analysis_results['summary']['total_size_mb']} MB")
    print(f"Total tables: {analyzer.analysis_results['summary']['total_tables']}")
    print(f"\nFiles generated:")
    print(f"- database_analysis.json (detailed analysis)")
    print(f"- DATABASE_DOCUMENTATION.md (comprehensive documentation)")

if __name__ == "__main__":
    main() 