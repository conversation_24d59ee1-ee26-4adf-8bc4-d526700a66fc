#!/usr/bin/env python3
"""
Test script to verify authentication is working after fresh start.
"""

import os
import sys
import sqlite3

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.user_management import authenticate_user, get_user_by_username
from app.utils import security

def test_authentication():
    """Test authentication with existing users."""
    
    print("Testing authentication after fresh start...")
    
    # Test users from the database
    test_users = [
        ("admin", "admin123"),
        ("olvida_alvin", "password123"),
        ("carlo", "password123")
    ]
    
    for username, password in test_users:
        print(f"\nTesting user: {username}")
        
        # First, check if user exists
        user = get_user_by_username(username)
        if user:
            print(f"  ✅ User found: {user.username} (ID: {user.user_id}, Role: {user.role}, Status: {user.account_status})")
            
            # Test authentication
            authenticated_user, error = authenticate_user(username, password)
            if authenticated_user:
                print(f"  ✅ Authentication successful for {username}")
            else:
                print(f"  ❌ Authentication failed for {username}: {error}")
        else:
            print(f"  ❌ User {username} not found")
    
    # Test with incorrect password
    print(f"\nTesting incorrect password for admin:")
    authenticated_user, error = authenticate_user("admin", "wrongpassword")
    if not authenticated_user:
        print(f"  ✅ Correctly rejected wrong password: {error}")
    else:
        print(f"  ❌ Incorrectly accepted wrong password")

if __name__ == "__main__":
    test_authentication()
