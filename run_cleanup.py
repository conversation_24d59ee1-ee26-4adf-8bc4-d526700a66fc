#!/usr/bin/env python3
"""
Simple script to run database cleanup and show results.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.health_monitor import get_health_monitor

def main():
    print("Starting comprehensive database cleanup...")
    
    try:
        monitor = get_health_monitor()
        results = monitor.cleanup_database_space()
        
        print(f"\n=== Cleanup Results ===")
        print(f"Success: {results['success']}")
        print(f"Databases processed: {results['databases_processed']}")
        print(f"Total space reclaimed: {results['total_space_reclaimed_mb']:.2f} MB")
        
        if results['errors']:
            print(f"\nErrors encountered:")
            for error in results['errors']:
                print(f"  - {error}")
        
        if results['details']:
            print(f"\nDetailed results:")
            for detail in results['details']:
                if detail['success']:
                    print(f"  - {detail['database']}: {detail['space_reclaimed_mb']:.2f} MB reclaimed")
                else:
                    print(f"  - {detail['database']}: Failed - {detail.get('error', 'Unknown error')}")
        
        return 0
        
    except Exception as e:
        print(f"Error during cleanup: {str(e)}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
