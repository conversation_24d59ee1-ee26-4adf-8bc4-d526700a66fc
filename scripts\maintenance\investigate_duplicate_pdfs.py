#!/usr/bin/env python3
"""
Investigate and Fix Duplicate PDF Documents

This script analyzes the pdf_documents table to identify and fix duplicate records
that are causing issues in the Associated PDF Documents section.

The script will:
1. Identify duplicate records by original_filename and category
2. Show the current state of the database
3. Provide options to clean up duplicates
4. Update the display logic to show only clean filenames
"""

import os
import sys
import sqlite3
import re
from collections import defaultdict
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.utils.content_db import get_db_connection

def analyze_database_state():
    """Analyze the current state of the pdf_documents table."""
    print("🔍 Analyzing Database State")
    print("=" * 50)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get all PDF documents
        cursor.execute('''
            SELECT id, filename, original_filename, category, form_id, created_at, updated_at
            FROM pdf_documents
            ORDER BY original_filename, category, created_at DESC
        ''')
        
        rows = cursor.fetchall()
        print(f"Total PDF documents in database: {len(rows)}")
        
        # Group by original_filename and category
        grouped_records = defaultdict(list)
        for row in rows:
            key = (row[2], row[3])  # (original_filename, category)
            grouped_records[key].append({
                'id': row[0],
                'filename': row[1],
                'original_filename': row[2],
                'category': row[3],
                'form_id': row[4],
                'created_at': row[5],
                'updated_at': row[6]
            })
        
        # Identify duplicates
        duplicates = {}
        clean_records = {}
        
        for key, records in grouped_records.items():
            if len(records) > 1:
                duplicates[key] = records
            else:
                clean_records[key] = records[0]
        
        print(f"\n📊 Analysis Results:")
        print(f"  Clean records (no duplicates): {len(clean_records)}")
        print(f"  Duplicate groups: {len(duplicates)}")
        
        if duplicates:
            print(f"\n🚨 DUPLICATE RECORDS FOUND:")
            print("-" * 50)
            for key, records in duplicates.items():
                original_filename, category = key
                print(f"\n📁 {original_filename} (Category: {category})")
                print(f"   Found {len(records)} duplicate records:")
                
                for i, record in enumerate(records, 1):
                    timestamp_info = ""
                    if record['filename'] != record['original_filename']:
                        # Check if filename has timestamp
                        if re.match(r'^\d{14}_', record['filename']):
                            timestamp_info = " (TIMESTAMPED)"
                        else:
                            timestamp_info = " (MODIFIED)"
                    
                    print(f"    {i}. ID: {record['id']}")
                    print(f"       Filename: {record['filename']}{timestamp_info}")
                    print(f"       Original: {record['original_filename']}")
                    print(f"       Form ID: {record['form_id']}")
                    print(f"       Created: {record['created_at']}")
                    print(f"       Updated: {record['updated_at']}")
        
        conn.close()
        return duplicates, clean_records
        
    except Exception as e:
        print(f"❌ Error analyzing database: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}, {}

def check_filesystem_state():
    """Check the actual filesystem state vs database records."""
    print(f"\n🔍 Checking Filesystem State")
    print("=" * 50)
    
    temp_folder = Path("data/temp")
    if not temp_folder.exists():
        print("❌ data/temp folder not found")
        return
    
    categories = ['CANOPY', 'RISE', 'MANUAL']
    filesystem_files = {}
    
    for category in categories:
        category_path = temp_folder / category
        if not category_path.exists():
            continue
            
        print(f"\n📁 Category: {category}")
        filesystem_files[category] = []
        
        # Check both old and new directory structures
        for item in category_path.iterdir():
            if item.is_file() and item.suffix.lower() == '.pdf':
                # Old structure: direct PDF files
                filesystem_files[category].append({
                    'path': str(item),
                    'filename': item.name,
                    'structure': 'old'
                })
            elif item.is_dir():
                # New structure: PDF files in subdirectories
                for pdf_file in item.glob('*.pdf'):
                    filesystem_files[category].append({
                        'path': str(pdf_file),
                        'filename': pdf_file.name,
                        'structure': 'new',
                        'subdir': item.name
                    })
        
        print(f"   Found {len(filesystem_files[category])} PDF files")
        for file_info in filesystem_files[category][:5]:  # Show first 5
            print(f"     - {file_info['filename']} ({file_info['structure']} structure)")
        if len(filesystem_files[category]) > 5:
            print(f"     ... and {len(filesystem_files[category]) - 5} more")
    
    return filesystem_files

def identify_orphaned_records(duplicates, filesystem_files):
    """Identify records that don't have corresponding files."""
    print(f"\n🔍 Identifying Orphaned Records")
    print("=" * 50)
    
    orphaned_records = []
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get all records
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
        ''')
        
        records = cursor.fetchall()
        
        for record in records:
            record_id, filename, original_filename, category = record
            found_in_filesystem = False
            
            if category in filesystem_files:
                for file_info in filesystem_files[category]:
                    # Check if the file exists in filesystem
                    if (file_info['filename'] == filename or 
                        file_info['filename'] == original_filename or
                        file_info['filename'].endswith(original_filename) or
                        original_filename in file_info['filename']):
                        found_in_filesystem = True
                        break
            
            if not found_in_filesystem:
                orphaned_records.append({
                    'id': record_id,
                    'filename': filename,
                    'original_filename': original_filename,
                    'category': category
                })
        
        if orphaned_records:
            print(f"🚨 Found {len(orphaned_records)} orphaned records:")
            for record in orphaned_records:
                print(f"   - ID: {record['id']}, Filename: {record['filename']}, Original: {record['original_filename']}, Category: {record['category']}")
        else:
            print("✅ No orphaned records found")
        
        conn.close()
        return orphaned_records
        
    except Exception as e:
        print(f"❌ Error identifying orphaned records: {str(e)}")
        return []

def cleanup_duplicates(duplicates, dry_run=True):
    """Clean up duplicate records, keeping only the most recent one."""
    print(f"\n🧹 Cleaning Up Duplicates")
    print("=" * 50)
    
    if not duplicates:
        print("✅ No duplicates to clean up")
        return
    
    if dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        total_deleted = 0
        
        for key, records in duplicates.items():
            original_filename, category = key
            print(f"\n📁 Processing: {original_filename} (Category: {category})")
            
            # Sort by created_at DESC to keep the most recent
            sorted_records = sorted(records, key=lambda x: x['created_at'], reverse=True)
            
            # Keep the first (most recent) record, delete the rest
            records_to_delete = sorted_records[1:]
            
            print(f"   Keeping record ID: {sorted_records[0]['id']} (most recent)")
            print(f"   Will delete {len(records_to_delete)} duplicate records:")
            
            for record in records_to_delete:
                print(f"     - ID: {record['id']}, Filename: {record['filename']}, Created: {record['created_at']}")
                
                if not dry_run:
                    cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (record['id'],))
                    total_deleted += 1
        
        if not dry_run and total_deleted > 0:
            conn.commit()
            print(f"\n✅ Successfully deleted {total_deleted} duplicate records")
        elif dry_run:
            print(f"\n🔍 Would delete {total_deleted} duplicate records in real mode")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error cleaning up duplicates: {str(e)}")
        import traceback
        traceback.print_exc()

def update_display_logic():
    """Update the display logic to show only clean filenames."""
    print(f"\n🔧 Updating Display Logic")
    print("=" * 50)
    
    # Check the current get_pdfs_by_form_id function
    forms_db_path = project_root / "app" / "utils" / "forms_db.py"
    
    if not forms_db_path.exists():
        print("❌ forms_db.py not found")
        return
    
    print("📝 Current get_pdfs_by_form_id function returns both filename and original_filename")
    print("   This is causing the duplicate display issue in Associated PDF Documents")
    
    # The issue is in the template that shows both filenames
    template_path = project_root / "app" / "templates" / "admin_form_preview.html"
    
    if template_path.exists():
        print(f"📄 Template file found: {template_path}")
        print("   The template shows both original_filename and filename (system filename)")
        print("   This should be updated to show only original_filename")
    else:
        print("❌ Template file not found")

def create_cleanup_report(duplicates, orphaned_records, filesystem_files):
    """Create a comprehensive cleanup report."""
    print(f"\n📋 Cleanup Report")
    print("=" * 50)
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'duplicates_found': len(duplicates),
        'orphaned_records': len(orphaned_records),
        'total_filesystem_files': sum(len(files) for files in filesystem_files.values()),
        'recommendations': []
    }
    
    if duplicates:
        report['recommendations'].append({
            'action': 'cleanup_duplicates',
            'description': f'Remove {sum(len(records) - 1 for records in duplicates.values())} duplicate records',
            'impact': 'Will clean up Associated PDF Documents display'
        })
    
    if orphaned_records:
        report['recommendations'].append({
            'action': 'cleanup_orphaned',
            'description': f'Remove {len(orphaned_records)} orphaned database records',
            'impact': 'Will clean up database consistency'
        })
    
    report['recommendations'].append({
        'action': 'update_display',
        'description': 'Update template to show only original_filename',
        'impact': 'Will improve user experience'
    })
    
    print(f"📊 Summary:")
    print(f"  - Duplicate groups: {report['duplicates_found']}")
    print(f"  - Orphaned records: {report['orphaned_records']}")
    print(f"  - Filesystem files: {report['total_filesystem_files']}")
    
    print(f"\n🎯 Recommendations:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"  {i}. {rec['action']}: {rec['description']}")
        print(f"     Impact: {rec['impact']}")
    
    return report

def main():
    """Main function to run the investigation and cleanup."""
    print("🔍 PDF Duplicate Documents Investigation")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Analyze database state
    duplicates, clean_records = analyze_database_state()
    
    # Step 2: Check filesystem state
    filesystem_files = check_filesystem_state()
    
    # Step 3: Identify orphaned records
    orphaned_records = identify_orphaned_records(duplicates, filesystem_files)
    
    # Step 4: Update display logic
    update_display_logic()
    
    # Step 5: Create cleanup report
    report = create_cleanup_report(duplicates, orphaned_records, filesystem_files)
    
    # Step 6: Offer cleanup options
    print(f"\n🎯 Cleanup Options")
    print("=" * 50)
    
    if duplicates or orphaned_records:
        print("The following cleanup actions are available:")
        
        if duplicates:
            print("1. Clean up duplicate records (keep most recent)")
        
        if orphaned_records:
            print("2. Remove orphaned database records")
        
        print("3. Update display logic to show only clean filenames")
        
        response = input("\nWould you like to proceed with cleanup? (y/N): ").strip().lower()
        
        if response in ['y', 'yes']:
            if duplicates:
                print("\n🧹 Cleaning up duplicates...")
                cleanup_duplicates(duplicates, dry_run=False)
            
            if orphaned_records:
                print("\n🧹 Cleaning up orphaned records...")
                # TODO: Implement orphaned record cleanup
                print("   (Orphaned record cleanup not implemented yet)")
            
            print("\n✅ Cleanup completed!")
        else:
            print("\n🔍 Cleanup skipped. Run with --cleanup flag to perform cleanup.")
    else:
        print("✅ No cleanup actions needed!")
    
    print(f"\n🏁 Investigation completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 