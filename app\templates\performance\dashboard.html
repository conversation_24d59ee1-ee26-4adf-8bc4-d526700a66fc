<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>ERDB Performance Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-critical { color: #dc3545; }
        .alert-badge {
            position: absolute;
            top: -5px;
            right: -5px;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .refresh-indicator {
            opacity: 0;
            transition: opacity 0.3s;
        }
        .refresh-indicator.active {
            opacity: 1;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-tachometer-alt"></i> ERDB Performance Dashboard
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light btn-sm me-2" onclick="exportMetrics()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-outline-light btn-sm me-2" onclick="optimizeDatabase()">
                    <i class="fas fa-database"></i> Optimize DB
                </button>
                <span class="refresh-indicator" id="refreshIndicator">
                    <i class="fas fa-sync-alt fa-spin text-light"></i>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-heartbeat"></i> System Health</h5>
                        <span id="healthStatus" class="badge bg-secondary">Loading...</span>
                    </div>
                    <div class="card-body">
                        <div class="row" id="systemMetrics">
                            <!-- System metrics will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Alerts -->
        <div class="row mb-4" id="alertsSection" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Performance Alerts</h5>
                    </div>
                    <div class="card-body" id="alertsContainer">
                        <!-- Alerts will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Function Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-memory"></i> Memory Usage</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="memoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Performance -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-database"></i> Database Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm" id="databaseTable">
                                <thead>
                                    <tr>
                                        <th>Table</th>
                                        <th>Rows</th>
                                        <th>Size (KB)</th>
                                        <th>Indexes</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Database stats will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-vector-square"></i> ChromaDB</h5>
                    </div>
                    <div class="card-body" id="chromaStats">
                        <!-- ChromaDB stats will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Function Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list"></i> Function Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm" id="functionsTable">
                                <thead>
                                    <tr>
                                        <th>Function</th>
                                        <th>Calls</th>
                                        <th>Avg Time (s)</th>
                                        <th>Max Time (s)</th>
                                        <th>Avg Memory (MB)</th>
                                        <th>Errors</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Function stats will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="successMessage"></div>
        </div>
        <div id="errorToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-exclamation-circle text-danger me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="errorMessage"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let performanceChart, memoryChart;
        let refreshInterval;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadDashboardData();
            startAutoRefresh();
        });

        function initializeCharts() {
            // Performance Chart
            const perfCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(perfCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Execution Time (s)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Memory Chart
            const memCtx = document.getElementById('memoryChart').getContext('2d');
            memoryChart = new Chart(memCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Used', 'Available'],
                    datasets: [{
                        data: [0, 100],
                        backgroundColor: ['#ff6384', '#36a2eb']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        function loadDashboardData() {
            showRefreshIndicator();
            
            Promise.all([
                fetch('/performance/api/system-metrics').then(r => r.json()),
                fetch('/performance/api/performance-metrics').then(r => r.json()),
                fetch('/performance/api/database-metrics').then(r => r.json()),
                fetch('/performance/api/chroma-metrics').then(r => r.json()),
                fetch('/performance/api/alerts').then(r => r.json())
            ]).then(([systemData, perfData, dbData, chromaData, alertsData]) => {
                updateSystemMetrics(systemData.data);
                updatePerformanceMetrics(perfData.data);
                updateDatabaseMetrics(dbData.data);
                updateChromaMetrics(chromaData.data);
                updateAlerts(alertsData.data);
                hideRefreshIndicator();
            }).catch(error => {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
                hideRefreshIndicator();
            });
        }

        function updateSystemMetrics(data) {
            const { system_metrics, health_status } = data;
            
            // Update health status badge
            const statusBadge = document.getElementById('healthStatus');
            statusBadge.textContent = health_status.status.toUpperCase();
            statusBadge.className = `badge bg-${getStatusColor(health_status.status)}`;
            
            // Update system metrics cards
            const metricsContainer = document.getElementById('systemMetrics');
            metricsContainer.innerHTML = `
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">CPU</h6>
                            <h4 class="text-${getCpuColor(system_metrics.cpu_percent)}">${system_metrics.cpu_percent.toFixed(1)}%</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">Memory</h6>
                            <h4 class="text-${getMemoryColor(system_metrics.memory_percent)}">${system_metrics.memory_percent.toFixed(1)}%</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">Disk</h6>
                            <h4 class="text-${getDiskColor(system_metrics.disk_usage_percent)}">${system_metrics.disk_usage_percent.toFixed(1)}%</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">DB Size</h6>
                            <h4 class="text-info">${system_metrics.database_size_mb.toFixed(1)} MB</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">Connections</h6>
                            <h4 class="text-primary">${system_metrics.active_connections}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card metric-card text-center">
                        <div class="card-body">
                            <h6 class="card-title">Uptime</h6>
                            <h4 class="text-success">${formatUptime(system_metrics.uptime_seconds)}</h4>
                        </div>
                    </div>
                </div>
            `;
            
            // Update memory chart
            memoryChart.data.datasets[0].data = [
                system_metrics.memory_percent,
                100 - system_metrics.memory_percent
            ];
            memoryChart.update();
        }

        function updatePerformanceMetrics(data) {
            const { function_statistics } = data;
            
            // Update functions table
            const tbody = document.querySelector('#functionsTable tbody');
            tbody.innerHTML = '';
            
            Object.entries(function_statistics).forEach(([funcName, stats]) => {
                const row = tbody.insertRow();
                const errorRate = stats.total_calls > 0 ? (stats.error_count / stats.total_calls * 100) : 0;
                const status = getPerformanceStatus(stats.avg_time, errorRate);
                
                row.innerHTML = `
                    <td><code>${funcName}</code></td>
                    <td>${stats.total_calls}</td>
                    <td>${stats.avg_time.toFixed(3)}</td>
                    <td>${stats.max_time.toFixed(3)}</td>
                    <td>${stats.avg_memory.toFixed(2)}</td>
                    <td>${stats.error_count}</td>
                    <td><span class="badge bg-${status.color}">${status.text}</span></td>
                `;
            });
        }

        function updateDatabaseMetrics(data) {
            const { table_statistics } = data;
            
            const tbody = document.querySelector('#databaseTable tbody');
            tbody.innerHTML = '';
            
            table_statistics.forEach(table => {
                const row = tbody.insertRow();
                const status = getTableStatus(table.row_count, table.size_kb);
                
                row.innerHTML = `
                    <td>${table.table_name}</td>
                    <td>${table.row_count.toLocaleString()}</td>
                    <td>${table.size_kb.toFixed(1)}</td>
                    <td>${table.index_count}</td>
                    <td><span class="badge bg-${status.color}">${status.text}</span></td>
                `;
            });
        }

        function updateChromaMetrics(data) {
            const { collection_statistics } = data;
            
            const container = document.getElementById('chromaStats');
            let html = '';
            
            Object.entries(collection_statistics).forEach(([collection, stats]) => {
                html += `
                    <div class="mb-3">
                        <h6>${collection}</h6>
                        <small class="text-muted">
                            Operations: ${stats.total_operations}<br>
                            Avg Time: ${stats.avg_time.toFixed(3)}s<br>
                            Searches: ${stats.search_operations}<br>
                            Errors: ${stats.error_count}
                        </small>
                    </div>
                `;
            });
            
            container.innerHTML = html || '<p class="text-muted">No ChromaDB metrics available</p>';
        }

        function updateAlerts(data) {
            const { alerts } = data;
            const alertsSection = document.getElementById('alertsSection');
            const alertsContainer = document.getElementById('alertsContainer');
            
            if (alerts.length > 0) {
                alertsSection.style.display = 'block';
                alertsContainer.innerHTML = alerts.map(alert => `
                    <div class="alert alert-${alert.type === 'critical' ? 'danger' : 'warning'} alert-dismissible fade show">
                        <strong>${alert.category}:</strong> ${alert.message}
                        ${alert.recommendations ? `<br><small>Recommendations: ${alert.recommendations.join(', ')}</small>` : ''}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `).join('');
            } else {
                alertsSection.style.display = 'none';
            }
        }

        // Utility functions
        function getStatusColor(status) {
            switch(status) {
                case 'healthy': return 'success';
                case 'warning': return 'warning';
                case 'critical': return 'danger';
                default: return 'secondary';
            }
        }

        function getCpuColor(percent) {
            if (percent > 90) return 'danger';
            if (percent > 70) return 'warning';
            return 'success';
        }

        function getMemoryColor(percent) {
            if (percent > 95) return 'danger';
            if (percent > 80) return 'warning';
            return 'success';
        }

        function getDiskColor(percent) {
            if (percent > 95) return 'danger';
            if (percent > 85) return 'warning';
            return 'success';
        }

        function getPerformanceStatus(avgTime, errorRate) {
            if (errorRate > 5 || avgTime > 5) return { color: 'danger', text: 'Poor' };
            if (errorRate > 1 || avgTime > 1) return { color: 'warning', text: 'Fair' };
            return { color: 'success', text: 'Good' };
        }

        function getTableStatus(rowCount, sizeKb) {
            if (sizeKb > 10000) return { color: 'warning', text: 'Large' };
            if (rowCount > 100000) return { color: 'info', text: 'High Volume' };
            return { color: 'success', text: 'Normal' };
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            if (days > 0) return `${days}d ${hours}h`;
            return `${hours}h`;
        }

        function showRefreshIndicator() {
            document.getElementById('refreshIndicator').classList.add('active');
        }

        function hideRefreshIndicator() {
            document.getElementById('refreshIndicator').classList.remove('active');
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
        }

        function exportMetrics() {
            fetch('/performance/api/export-metrics')
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('Metrics exported successfully');
                    } else {
                        showError(data.error);
                    }
                })
                .catch(error => showError('Export failed: ' + error.message));
        }

        function optimizeDatabase() {
            fetch('/performance/api/optimize-database', { 
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                }
            })
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(data.data.message);
                        loadDashboardData(); // Refresh data
                    } else {
                        showError(data.error);
                    }
                })
                .catch(error => showError('Optimization failed: ' + error.message));
        }

        function showSuccess(message) {
            document.getElementById('successMessage').textContent = message;
            new bootstrap.Toast(document.getElementById('successToast')).show();
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            new bootstrap.Toast(document.getElementById('errorToast')).show();
        }
    </script>
</body>
</html>
