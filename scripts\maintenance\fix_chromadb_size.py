#!/usr/bin/env python3
"""
Fix ChromaDB Size Issue Script

This script addresses the issue where ChromaDB file size doesn't decrease after PDF deletions.
It performs the following operations:
1. Identifies and removes orphaned vector embeddings
2. Runs VACUUM operations to reclaim space
3. Provides detailed reporting on the cleanup process
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.unified_vector_db import get_unified_vector_db
from app.utils.helpers import vacuum_chromadb
import chromadb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_chromadb_size.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_chromadb_size():
    """Get the current size of the ChromaDB file."""
    db_path = "./data/unified_chroma/chroma.sqlite3"
    if os.path.exists(db_path):
        size_mb = os.path.getsize(db_path) / (1024 * 1024)
        return size_mb
    return 0

def get_collection_stats():
    """Get statistics about the unified collection."""
    try:
        unified_db = get_unified_vector_db()
        stats = unified_db.get_collection_stats()
        return stats
    except Exception as e:
        logger.error(f"Failed to get collection stats: {str(e)}")
        return None

def cleanup_orphaned_vectors():
    """Clean up orphaned vector embeddings."""
    try:
        from scripts.maintenance.cleanup_orphaned_vectors import cleanup_orphaned_vectors as cleanup_func
        deleted_count = cleanup_func(dry_run=False)
        return deleted_count
    except Exception as e:
        logger.error(f"Failed to cleanup orphaned vectors: {str(e)}")
        return 0

def run_vacuum_operation():
    """Run VACUUM operation on ChromaDB."""
    try:
        logger.info("Running VACUUM operation on ChromaDB...")
        success, size_before, size_after, space_reclaimed = vacuum_chromadb()
        
        if success:
            logger.info(f"VACUUM completed successfully:")
            logger.info(f"  Before: {size_before:.2f} MB")
            logger.info(f"  After: {size_after:.2f} MB")
            logger.info(f"  Space reclaimed: {space_reclaimed:.2f} MB")
            return True, space_reclaimed
        else:
            logger.error("VACUUM operation failed")
            return False, 0
            
    except Exception as e:
        logger.error(f"VACUUM operation failed: {str(e)}")
        return False, 0

def main():
    """Main function to fix ChromaDB size issue."""
    logger.info("=== ChromaDB Size Fix Script ===")
    
    # Get initial stats
    initial_size = get_chromadb_size()
    logger.info(f"Initial ChromaDB size: {initial_size:.2f} MB")
    
    # Get collection statistics
    stats = get_collection_stats()
    if stats:
        total_docs = stats.get('total_documents', 0)
        categories = stats.get('categories', {})
        logger.info(f"Total documents in collection: {total_docs}")
        if categories:
            logger.info(f"Categories: {list(categories.keys())}")
            for category, count in categories.items():
                logger.info(f"  {category}: {count} documents")
    
    # Step 1: Clean up orphaned vectors
    logger.info("\n=== Step 1: Cleaning up orphaned vectors ===")
    deleted_count = cleanup_orphaned_vectors()
    logger.info(f"Deleted {deleted_count} orphaned vectors")
    
    # Step 2: Run VACUUM operation
    logger.info("\n=== Step 2: Running VACUUM operation ===")
    vacuum_success, space_reclaimed = run_vacuum_operation()
    
    # Get final stats
    final_size = get_chromadb_size()
    logger.info(f"\n=== Results ===")
    logger.info(f"Initial size: {initial_size:.2f} MB")
    logger.info(f"Final size: {final_size:.2f} MB")
    logger.info(f"Total space reclaimed: {initial_size - final_size:.2f} MB")
    logger.info(f"Orphaned vectors deleted: {deleted_count}")
    
    if vacuum_success:
        logger.info("✅ ChromaDB size fix completed successfully!")
    else:
        logger.warning("⚠️  ChromaDB size fix completed with warnings (VACUUM failed)")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        sys.exit(1)
