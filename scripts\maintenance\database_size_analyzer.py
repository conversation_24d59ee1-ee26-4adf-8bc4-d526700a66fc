#!/usr/bin/env python3
"""
Database Size Analyzer for ERDB System

This script provides comprehensive analysis of database sizes, establishes baselines,
and tracks size changes over time. It helps identify space usage patterns and
determine the true baseline sizes for all databases.
"""

import os
import sys
import json
import sqlite3
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_size_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseSizeAnalyzer:
    """Comprehensive database size analysis and baseline tracking."""
    
    def __init__(self):
        self.databases = {
            'main': './erdb_main.db',
            'chat_history': './chat_history.db',
            'user_management': './user_management.db',
            'scraped_pages': './scraped_pages.db',
            'content_db': './data/content_db.sqlite',
            'erdb_legacy': './data/erdb.db',
            'chroma_db': './data/unified_chroma/chroma.sqlite3',
            'itis_db': './data/itisSqlite060625/ITIS.sqlite'
        }
        
        self.baseline_file = './database_baselines.json'
        self.history_file = './database_size_history.json'
        
    def analyze_all_databases(self) -> Dict[str, Any]:
        """Analyze all databases and return comprehensive statistics."""
        logger.info("Starting comprehensive database analysis...")
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'databases': {},
            'summary': {
                'total_size_mb': 0,
                'total_databases': 0,
                'active_databases': 0,
                'largest_database': None,
                'smallest_database': None
            }
        }
        
        total_size = 0
        active_count = 0
        sizes = []
        
        for name, path in self.databases.items():
            db_info = self._analyze_single_database(name, path)
            analysis['databases'][name] = db_info
            
            if db_info['exists']:
                active_count += 1
                total_size += db_info['size_mb']
                sizes.append((name, db_info['size_mb']))
        
        # Calculate summary statistics
        analysis['summary']['total_size_mb'] = total_size
        analysis['summary']['total_databases'] = len(self.databases)
        analysis['summary']['active_databases'] = active_count
        
        if sizes:
            sizes.sort(key=lambda x: x[1], reverse=True)
            analysis['summary']['largest_database'] = {
                'name': sizes[0][0],
                'size_mb': sizes[0][1]
            }
            analysis['summary']['smallest_database'] = {
                'name': sizes[-1][0],
                'size_mb': sizes[-1][1]
            }
        
        logger.info(f"Analysis complete. Total size: {total_size:.2f} MB across {active_count} active databases")
        return analysis
    
    def _analyze_single_database(self, name: str, path: str) -> Dict[str, Any]:
        """Analyze a single database and return detailed information."""
        db_info = {
            'name': name,
            'path': path,
            'exists': False,
            'size_mb': 0,
            'size_bytes': 0,
            'last_modified': None,
            'tables': [],
            'row_counts': {},
            'integrity_check': False,
            'vacuum_needed': False,
            'free_pages': 0,
            'total_pages': 0
        }
        
        if not os.path.exists(path):
            logger.debug(f"Database {name} not found at {path}")
            return db_info
        
        try:
            # Get file information
            stat = os.stat(path)
            db_info['exists'] = True
            db_info['size_bytes'] = stat.st_size
            db_info['size_mb'] = stat.st_size / (1024 * 1024)
            db_info['last_modified'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
            
            # Analyze SQLite database if it's a valid SQLite file
            if self._is_sqlite_database(path):
                sqlite_info = self._analyze_sqlite_database(path)
                db_info.update(sqlite_info)
            
            logger.debug(f"Analyzed {name}: {db_info['size_mb']:.2f} MB")
            
        except Exception as e:
            logger.error(f"Error analyzing database {name}: {str(e)}")
        
        return db_info
    
    def _is_sqlite_database(self, path: str) -> bool:
        """Check if a file is a valid SQLite database."""
        try:
            if not os.path.exists(path):
                return False
            
            # Check file header
            with open(path, 'rb') as f:
                header = f.read(16)
                return header.startswith(b'SQLite format 3')
        except:
            return False
    
    def _analyze_sqlite_database(self, path: str) -> Dict[str, Any]:
        """Analyze SQLite database structure and statistics."""
        info = {
            'tables': [],
            'row_counts': {},
            'integrity_check': False,
            'vacuum_needed': False,
            'free_pages': 0,
            'total_pages': 0,
            'page_size': 0,
            'journal_mode': 'unknown'
        }
        
        try:
            conn = sqlite3.connect(path)
            cursor = conn.cursor()
            
            # Get database statistics
            cursor.execute("PRAGMA page_count")
            info['total_pages'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            info['page_size'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA freelist_count")
            info['free_pages'] = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA journal_mode")
            info['journal_mode'] = cursor.fetchone()[0]
            
            # Check if vacuum is needed (more than 10% free pages)
            if info['total_pages'] > 0:
                free_percentage = info['free_pages'] / info['total_pages']
                info['vacuum_needed'] = free_percentage > 0.1
            
            # Get table information
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            tables = cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                info['tables'].append(table_name)
                
                # Get row count for each table
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    info['row_counts'][table_name] = row_count
                except:
                    info['row_counts'][table_name] = 0
            
            # Check integrity
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()
            info['integrity_check'] = integrity_result[0] == 'ok' if integrity_result else False
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error analyzing SQLite database {path}: {str(e)}")
        
        return info
    
    def establish_baselines(self) -> Dict[str, Any]:
        """Establish baseline sizes for all databases."""
        logger.info("Establishing database baselines...")
        
        analysis = self.analyze_all_databases()
        
        baselines = {
            'established_at': datetime.now().isoformat(),
            'databases': {}
        }
        
        for name, db_info in analysis['databases'].items():
            if db_info['exists']:
                baselines['databases'][name] = {
                    'baseline_size_mb': db_info['size_mb'],
                    'baseline_size_bytes': db_info['size_bytes'],
                    'path': db_info['path'],
                    'tables': db_info.get('tables', []),
                    'row_counts': db_info.get('row_counts', {})
                }
        
        # Save baselines
        with open(self.baseline_file, 'w') as f:
            json.dump(baselines, f, indent=2)
        
        logger.info(f"Baselines established and saved to {self.baseline_file}")
        return baselines
    
    def track_size_changes(self) -> Dict[str, Any]:
        """Track size changes since baseline establishment."""
        logger.info("Tracking database size changes...")
        
        # Load baselines
        if not os.path.exists(self.baseline_file):
            logger.warning("No baselines found. Establishing new baselines...")
            self.establish_baselines()
        
        with open(self.baseline_file, 'r') as f:
            baselines = json.load(f)
        
        # Get current analysis
        current_analysis = self.analyze_all_databases()
        
        # Calculate changes
        changes = {
            'timestamp': datetime.now().isoformat(),
            'changes': {},
            'summary': {
                'total_change_mb': 0,
                'databases_with_changes': 0,
                'largest_increase': None,
                'largest_decrease': None
            }
        }
        
        total_change = 0
        changes_list = []
        
        for name, current_info in current_analysis['databases'].items():
            if not current_info['exists']:
                continue
            
            baseline_info = baselines['databases'].get(name, {})
            baseline_size = baseline_info.get('baseline_size_mb', 0)
            current_size = current_info['size_mb']
            
            change_mb = current_size - baseline_size
            change_percentage = (change_mb / baseline_size * 100) if baseline_size > 0 else 0
            
            change_info = {
                'name': name,
                'baseline_size_mb': baseline_size,
                'current_size_mb': current_size,
                'change_mb': change_mb,
                'change_percentage': change_percentage,
                'path': current_info['path']
            }
            
            changes['changes'][name] = change_info
            
            if abs(change_mb) > 0.01:  # Only count significant changes (>10KB)
                total_change += change_mb
                changes_list.append((name, change_mb))
        
        # Calculate summary
        changes['summary']['total_change_mb'] = total_change
        changes['summary']['databases_with_changes'] = len([c for c in changes_list if abs(c[1]) > 0.01])
        
        if changes_list:
            changes_list.sort(key=lambda x: x[1], reverse=True)
            changes['summary']['largest_increase'] = {
                'name': changes_list[0][0],
                'change_mb': changes_list[0][1]
            }
            changes['summary']['largest_decrease'] = {
                'name': changes_list[-1][0],
                'change_mb': changes_list[-1][1]
            }
        
        # Save to history
        self._save_to_history(changes)
        
        logger.info(f"Size tracking complete. Total change: {total_change:.2f} MB")
        return changes
    
    def _save_to_history(self, changes: Dict[str, Any]):
        """Save size changes to history file."""
        history = []
        
        if os.path.exists(self.history_file):
            with open(self.history_file, 'r') as f:
                history = json.load(f)
        
        history.append(changes)
        
        # Keep only last 100 entries
        if len(history) > 100:
            history = history[-100:]
        
        with open(self.history_file, 'w') as f:
            json.dump(history, f, indent=2)
    
    def get_recommendations(self) -> List[str]:
        """Get recommendations based on current analysis."""
        recommendations = []
        
        analysis = self.analyze_all_databases()
        
        for name, db_info in analysis['databases'].items():
            if not db_info['exists']:
                continue
            
            # Check for large databases
            if db_info['size_mb'] > 100:
                recommendations.append(f"Database {name} is large ({db_info['size_mb']:.1f} MB). Consider archiving old data.")
            
            # Check for vacuum needed
            if db_info.get('vacuum_needed', False):
                recommendations.append(f"Database {name} needs VACUUM operation ({(db_info['free_pages']/db_info['total_pages']*100):.1f}% free pages).")
            
            # Check for integrity issues
            if not db_info.get('integrity_check', True):
                recommendations.append(f"Database {name} has integrity issues. Run integrity check.")
        
        return recommendations
    
    def generate_report(self) -> str:
        """Generate a comprehensive report."""
        logger.info("Generating comprehensive database report...")
        
        analysis = self.analyze_all_databases()
        changes = self.track_size_changes()
        recommendations = self.get_recommendations()
        
        report = f"""
# Database Size Analysis Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary
- Total Databases: {analysis['summary']['total_databases']}
- Active Databases: {analysis['summary']['active_databases']}
- Total Size: {analysis['summary']['total_size_mb']:.2f} MB
- Largest Database: {analysis['summary']['largest_database']['name'] if analysis['summary']['largest_database'] else 'N/A'} ({analysis['summary']['largest_database']['size_mb']:.2f} MB if analysis['summary']['largest_database'] else 0)

## Database Details
"""
        
        for name, db_info in analysis['databases'].items():
            if db_info['exists']:
                report += f"""
### {name.upper()}
- Path: {db_info['path']}
- Size: {db_info['size_mb']:.2f} MB
- Tables: {len(db_info.get('tables', []))}
- Last Modified: {db_info['last_modified']}
- Integrity: {'OK' if db_info.get('integrity_check', False) else 'ISSUES'}
- Vacuum Needed: {'Yes' if db_info.get('vacuum_needed', False) else 'No'}
"""
                
                if name in changes['changes']:
                    change_info = changes['changes'][name]
                    report += f"- Change from Baseline: {change_info['change_mb']:+.2f} MB ({change_info['change_percentage']:+.1f}%)\n"
        
        if recommendations:
            report += "\n## Recommendations\n"
            for rec in recommendations:
                report += f"- {rec}\n"
        
        return report

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database Size Analyzer')
    parser.add_argument('--analyze', action='store_true', help='Analyze all databases')
    parser.add_argument('--baseline', action='store_true', help='Establish baselines')
    parser.add_argument('--track', action='store_true', help='Track size changes')
    parser.add_argument('--report', action='store_true', help='Generate comprehensive report')
    parser.add_argument('--output', type=str, help='Output file for report')
    
    args = parser.parse_args()
    
    analyzer = DatabaseSizeAnalyzer()
    
    if args.analyze:
        analysis = analyzer.analyze_all_databases()
        print(json.dumps(analysis, indent=2))
    
    if args.baseline:
        baselines = analyzer.establish_baselines()
        print(f"Baselines established: {baselines['established_at']}")
    
    if args.track:
        changes = analyzer.track_size_changes()
        print(json.dumps(changes, indent=2))
    
    if args.report:
        report = analyzer.generate_report()
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"Report saved to {args.output}")
        else:
            print(report)
    
    if not any([args.analyze, args.baseline, args.track, args.report]):
        # Default: run full analysis and report
        analysis = analyzer.analyze_all_databases()
        changes = analyzer.track_size_changes()
        recommendations = analyzer.get_recommendations()
        
        print(f"\n=== Database Size Analysis ===")
        print(f"Total Size: {analysis['summary']['total_size_mb']:.2f} MB")
        print(f"Active Databases: {analysis['summary']['active_databases']}")
        
        if recommendations:
            print(f"\n=== Recommendations ===")
            for rec in recommendations:
                print(f"- {rec}")

if __name__ == '__main__':
    main()
