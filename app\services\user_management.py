"""
User service module for the user management system.

This module provides functions for user CRUD operations and related functionality.
"""

import os
import datetime
import logging
import uuid
import re
from typing import Tuple, List, Dict, Any, Optional, Union
from email_validator import validate_email, EmailNotValidError
from flask import request

from app.utils import db_connection as db
from app.utils import security
from app.models.user import User, PermissionGroup
from app.utils.permissions import log_permission_change

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Profile picture settings
PROFILE_PICS_DIR = os.getenv("PROFILE_PICS_DIR", "./data/temp/profile_pics")
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_PROFILE_PIC_SIZE = 2 * 1024 * 1024  # 2MB

# Database path
USER_DB_PATH = os.getenv("USER_DB_PATH", "./user_management.db")

# Email verification settings
EMAIL_VERIFY_EXPIRY = int(os.getenv("EMAIL_VERIFY_EXPIRY", "86400"))  # 24 hours
PASSWORD_RESET_EXPIRY = int(os.getenv("PASSWORD_RESET_EXPIRY", "3600"))  # 1 hour


def get_user_by_id(user_id: Union[int, str]) -> Optional[User]:
    """
    Get a user by ID.

    Args:
        user_id: The ID of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_id = int(user_id)
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at, password_hash
            FROM users
            WHERE user_id = ?
        """, (user_id,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by ID: {str(e)}")
        return None


def get_user_by_username(username: str) -> Optional[User]:
    """
    Get a user by username.

    Args:
        username: The username of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at, password_hash
            FROM users
            WHERE username = ?
        """, (username,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by username: {str(e)}")
        return None


def get_user_by_email(email: str) -> Optional[User]:
    """
    Get a user by email.

    Args:
        email: The email of the user to get

    Returns:
        A User object if found, None otherwise
    """
    try:
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, profile_picture, full_name, group_id,
                   password_changed_at, password_hash
            FROM users
            WHERE email = ?
        """, (email,))

        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Failed to get user by email: {str(e)}")
        return None


def get_all_users(page: int = 1, per_page: int = 25, search: Optional[str] = None,
                 sort_by: str = 'username', sort_order: str = 'asc') -> Tuple[List[Dict[str, Any]], int]:
    """
    Get all users with pagination and search.

    Args:
        page: The page number
        per_page: The number of users per page
        search: Search term to filter users
        sort_by: Column to sort by
        sort_order: Sort order ('asc' or 'desc')

    Returns:
        A tuple of (users, total_count)
    """
    try:
        # Validate sort parameters
        valid_sort_columns = ['username', 'email', 'role', 'account_status', 'created_at', 'last_login']
        if sort_by not in valid_sort_columns:
            sort_by = 'username'

        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'asc'

        # Base query
        query = """
            SELECT user_id, username, email, role, account_status,
                   created_at, last_login, failed_login_attempts,
                   email_verified, full_name, profile_picture, group_id
            FROM users
        """

        count_query = "SELECT COUNT(*) as count FROM users"

        params = []

        # Add search condition if provided
        if search:
            search_condition = " WHERE username LIKE ? OR email LIKE ? OR full_name LIKE ?"
            query += search_condition
            count_query += search_condition
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        # Add sorting
        query += f" ORDER BY {sort_by} {sort_order}"

        # Add pagination
        query += " LIMIT ? OFFSET ?"
        params.extend([per_page, (page - 1) * per_page])

        # Get users
        users = db.execute_query(query, tuple(params))

        # Get total count
        count_result = db.execute_query(count_query, tuple(params[:3] if search else []))
        total_count = count_result[0]['count'] if count_result else 0

        # Add group information
        for user in users:
            if user.get('group_id'):
                group_data = db.execute_query(
                    "SELECT name FROM permission_groups WHERE group_id = ?",
                    (user['group_id'],)
                )
                user['group_name'] = group_data[0]['name'] if group_data else None
            else:
                user['group_name'] = None

        return users, total_count
    except Exception as e:
        logger.error(f"Failed to get users: {str(e)}")
        return [], 0


def register_user(username: str, email: str, password: str, role: str = 'viewer',
                 full_name: Optional[str] = None, require_approval: bool = True) -> Tuple[bool, str]:
    """
    Register a new user.

    Args:
        username: The username for the new user
        email: The email for the new user
        password: The password for the new user
        role: The role for the new user
        full_name: The full name for the new user
        require_approval: Whether admin approval is required

    Returns:
        A tuple of (success, message)
    """
    try:
        # Validate username (3-20 alphanumeric characters)
        if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
            return False, "Username must be 3-20 alphanumeric characters or underscores"

        # Validate email
        try:
            validate_email(email)
        except EmailNotValidError:
            return False, "Invalid email address"

        # Validate password complexity
        is_valid, error_message = security.validate_password_complexity(password)
        if not is_valid:
            return False, error_message

        # Check if username or email already exists
        existing = db.execute_query(
            "SELECT username, email FROM users WHERE username = ? OR email = ?",
            (username, email)
        )

        if existing:
            if existing[0]['username'] == username:
                return False, "Username already exists"
            if existing[0]['email'] == email:
                return False, "Email already exists"

        # Hash the password
        password_hash = security.hash_password(password)

        # Generate email verification token
        verification_token = str(uuid.uuid4())
        verification_expiry = (datetime.datetime.now() + datetime.timedelta(seconds=security.EMAIL_VERIFY_EXPIRY)).isoformat()

        # Set initial account status
        account_status = 'pending' if require_approval else 'active'

        # Insert the new user
        user_id = db.execute_insert('''
            INSERT INTO users (
                username, password_hash, email, role, account_status,
                verification_token, verification_token_expiry, full_name,
                password_changed_at, email_verified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            username,
            password_hash,
            email,
            role,
            account_status,
            verification_token,
            verification_expiry,
            full_name,
            datetime.datetime.now().isoformat(),  # Set password_changed_at to now
            0  # Not verified yet
        ))

        # Automatically assign users to the corresponding permission group based on role
        if role in ['editor', 'viewer']:
            group_name = 'Editor' if role == 'editor' else 'Viewer'
            group_data = db.execute_query(
                "SELECT group_id FROM permission_groups WHERE name = ?",
                (group_name,)
            )

            if group_data and group_data[0]['group_id']:
                group_id = group_data[0]['group_id']
                db.execute_update(
                    "UPDATE users SET group_id = ? WHERE user_id = ?",
                    (group_id, user_id)
                )

        # Log the user creation
        log_user_activity(
            user_id=user_id,
            action_type="user_created",
            details=f"User created with role {role}",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        # Send verification email
        send_verification_email(username, email, verification_token)

        return True, "User registered successfully"
    except Exception as e:
        logger.error(f"Failed to register user: {str(e)}")
        return False, f"An error occurred during registration: {str(e)}"


def send_verification_email(username: str, email: str, token: str) -> bool:
    """
    Send an email verification email.

    Args:
        username: The username of the user
        email: The email address to send to
        token: The verification token

    Returns:
        True if successful, False otherwise
    """
    try:
        from flask import request
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Email configuration
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME", "")
        smtp_password = os.getenv("SMTP_PASSWORD", "")
        smtp_from_email = os.getenv("SMTP_FROM_EMAIL", "<EMAIL>")

        # Skip sending email if SMTP credentials are not configured
        if not smtp_username or not smtp_password:
            logger.warning("SMTP credentials not configured. Skipping verification email.")
            return True

        # Create verification URL
        verification_url = f"{request.host_url.rstrip('/')}/verify_email/{token}"

        # Create email message
        message = MIMEMultipart()
        message["From"] = smtp_from_email
        message["To"] = email
        message["Subject"] = "Verify Your Email Address"

        # Email body
        body = f"""
        <html>
        <body>
            <h2>Welcome to the Document Management System, {username}!</h2>
            <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
            <p><a href="{verification_url}">Verify Email Address</a></p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not register for an account, please ignore this email.</p>
        </body>
        </html>
        """

        message.attach(MIMEText(body, "html"))

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(message)

        return True
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}")
        return False


def log_user_activity(user_id: int, action_type: str, details: str,
                     status: str = 'success', ip_address: Optional[str] = None) -> bool:
    """
    Log user activity.

    Args:
        user_id: The ID of the user
        action_type: The type of action
        details: Details about the action
        status: The status of the action
        ip_address: The IP address of the user

    Returns:
        True if successful, False otherwise
    """
    try:
        db.execute_insert("""
            INSERT INTO user_activity_logs (
                user_id, action_type, details, status, ip_address, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            user_id,
            action_type,
            details,
            status,
            ip_address,
            datetime.datetime.now().isoformat()
        ))

        return True
    except Exception as e:
        logger.error(f"Failed to log user activity: {str(e)}")
        return False


# authenticate_user function moved to app.routes.auth for comprehensive security and logging
# Use auth.authenticate_user instead of this simplified version


def update_session_end(session_id: str) -> bool:
    """
    Update session end time.

    Args:
        session_id: The session ID

    Returns:
        True if successful, False otherwise
    """
    try:
        db.execute_update(
            "UPDATE user_sessions SET end_time = ? WHERE session_id = ?",
            (datetime.datetime.now().isoformat(), session_id)
        )
        return True
    except Exception as e:
        logger.error(f"Failed to update session end: {str(e)}")
        return False


def verify_email(token: str) -> Tuple[bool, str]:
    """
    Verify user email with token.

    Args:
        token: The verification token

    Returns:
        A tuple of (success, message)
    """
    try:
        user_data = db.execute_query(
            "SELECT user_id, username, email, verification_token_expiry FROM users WHERE verification_token = ?",
            (token,)
        )

        if not user_data:
            return False, "Invalid verification token"

        user = user_data[0]
        expiry = datetime.datetime.fromisoformat(user['verification_token_expiry'])

        if datetime.datetime.now() > expiry:
            return False, "Verification token has expired"

        # Update user as verified
        db.execute_update(
            "UPDATE users SET email_verified = 1, verification_token = NULL, verification_token_expiry = NULL WHERE user_id = ?",
            (user['user_id'],)
        )

        return True, "Email verified successfully"
    except Exception as e:
        logger.error(f"Failed to verify email: {str(e)}")
        return False, "Email verification failed"


def generate_password_reset_token(user_id: int) -> str:
    """
    Generate a password reset token for a user.

    Args:
        user_id: The user ID

    Returns:
        The reset token
    """
    try:
        token = str(uuid.uuid4())
        expiry = (datetime.datetime.now() + datetime.timedelta(seconds=PASSWORD_RESET_EXPIRY)).isoformat()

        db.execute_update(
            "UPDATE users SET reset_token = ?, reset_token_expiry = ? WHERE user_id = ?",
            (token, expiry, user_id)
        )

        return token
    except Exception as e:
        logger.error(f"Failed to generate password reset token: {str(e)}")
        return ""


def send_password_reset_email(email: str, username: str, token: str) -> bool:
    """
    Send a password reset email.

    Args:
        email: The email address
        username: The username
        token: The reset token

    Returns:
        True if successful, False otherwise
    """
    try:
        from flask import request
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Email configuration
        smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        smtp_port = int(os.getenv("SMTP_PORT", "587"))
        smtp_username = os.getenv("SMTP_USERNAME", "")
        smtp_password = os.getenv("SMTP_PASSWORD", "")
        smtp_from_email = os.getenv("SMTP_FROM_EMAIL", "<EMAIL>")

        # Skip sending email if SMTP credentials are not configured
        if not smtp_username or not smtp_password:
            logger.warning("SMTP credentials not configured. Skipping password reset email.")
            return True

        # Create reset URL
        reset_url = f"{request.host_url.rstrip('/')}/reset_password/{token}"

        # Create email message
        message = MIMEMultipart()
        message["From"] = smtp_from_email
        message["To"] = email
        message["Subject"] = "Password Reset Request"

        # Email body
        body = f"""
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>Hello {username},</p>
            <p>You have requested to reset your password. Click the link below to reset it:</p>
            <p><a href="{reset_url}">Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you did not request this reset, please ignore this email.</p>
        </body>
        </html>
        """

        message.attach(MIMEText(body, "html"))

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(message)

        return True
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}")
        return False


def verify_password_reset_token(token: str) -> Tuple[Optional[int], Optional[str]]:
    """
    Verify a password reset token.

    Args:
        token: The reset token

    Returns:
        A tuple of (user_id, error_message)
    """
    try:
        user_data = db.execute_query(
            "SELECT user_id, reset_token_expiry FROM users WHERE reset_token = ?",
            (token,)
        )

        if not user_data:
            return None, "Invalid reset token"

        user = user_data[0]
        expiry = datetime.datetime.fromisoformat(user['reset_token_expiry'])

        if datetime.datetime.now() > expiry:
            return None, "Reset token has expired"

        return user['user_id'], None
    except Exception as e:
        logger.error(f"Failed to verify password reset token: {str(e)}")
        return None, "Token verification failed"


def reset_password(user_id: int, new_password: str) -> Tuple[bool, str]:
    """
    Reset a user's password.

    Args:
        user_id: The user ID
        new_password: The new password

    Returns:
        A tuple of (success, message)
    """
    try:
        # Validate password complexity
        is_valid, error_message = security.validate_password_complexity(new_password)
        if not is_valid:
            return False, error_message

        # Hash the new password
        password_hash = security.hash_password(new_password)

        # Update password and clear reset token
        db.execute_update(
            "UPDATE users SET password_hash = ?, reset_token = NULL, reset_token_expiry = NULL, password_changed_at = ? WHERE user_id = ?",
            (password_hash, datetime.datetime.now().isoformat(), user_id)
        )

        return True, "Password reset successfully"
    except Exception as e:
        logger.error(f"Failed to reset password: {str(e)}")
        return False, "Password reset failed"


def update_user(user_id: int, data: Dict[str, Any], admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update user information.

    Args:
        user_id: The user ID to update
        data: Dictionary containing user data to update
        admin_user_id: ID of the admin making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        # Get current user data
        current_user = get_user_by_id(user_id)
        if not current_user:
            return False, "User not found"

        # Build update query
        update_fields = []
        params = []

        # Handle different field updates
        if 'username' in data and data['username'] != current_user.username:
            # Check if username is already taken
            existing = get_user_by_username(data['username'])
            if existing and existing.user_id != user_id:
                return False, "Username already exists"
            update_fields.append("username = ?")
            params.append(data['username'])

        if 'email' in data and data['email'] != current_user.email:
            # Validate email
            try:
                validate_email(data['email'])
            except EmailNotValidError:
                return False, "Invalid email address"
            
            # Check if email is already taken
            existing = get_user_by_email(data['email'])
            if existing and existing.user_id != user_id:
                return False, "Email already exists"
            update_fields.append("email = ?")
            params.append(data['email'])

        if 'full_name' in data:
            update_fields.append("full_name = ?")
            params.append(data['full_name'])

        if 'role' in data:
            update_fields.append("role = ?")
            params.append(data['role'])

        if 'account_status' in data:
            update_fields.append("account_status = ?")
            params.append(data['account_status'])

        if 'group_id' in data:
            update_fields.append("group_id = ?")
            params.append(data['group_id'])

        # Update profile picture if provided
        if 'profile_picture' in data:
            update_fields.append("profile_picture = ?")
            params.append(data['profile_picture'])

        if not update_fields:
            return True, "No changes to update"

        # Add user_id to params
        params.append(user_id)

        # Execute update
        query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
        db.execute_update(query, tuple(params))

        # Log the activity
        log_user_activity(
            user_id=admin_user_id or user_id,
            action_type="user_updated",
            details=f"Updated user {user_id}",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        return True, "User updated successfully"
    except Exception as e:
        logger.error(f"Failed to update user: {str(e)}")
        return False, f"Failed to update user: {str(e)}"


def delete_user(user_id: int) -> Tuple[bool, str]:
    """
    Delete a user.

    Args:
        user_id: The user ID to delete

    Returns:
        A tuple of (success, message)
    """
    try:
        # Check if user exists
        user = get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        # Delete user's permission overrides
        db.execute_update("DELETE FROM permission_overrides WHERE user_id = ?", (user_id,))

        # Delete user's dashboard permissions
        db.execute_update("DELETE FROM dashboard_permissions WHERE user_id = ?", (user_id,))

        # Delete user's activity logs
        db.execute_update("DELETE FROM user_activity_logs WHERE user_id = ?", (user_id,))

        # Delete user's sessions
        db.execute_update("DELETE FROM user_sessions WHERE user_id = ?", (user_id,))

        # Delete the user
        db.execute_update("DELETE FROM users WHERE user_id = ?", (user_id,))

        return True, "User deleted successfully"
    except Exception as e:
        logger.error(f"Failed to delete user: {str(e)}")
        return False, f"Failed to delete user: {str(e)}"


def bulk_update_users(user_ids: List[int], action: str, value: str, admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Bulk update multiple users.

    Args:
        user_ids: List of user IDs to update
        action: The action to perform (status, role, delete)
        value: The value for the action
        admin_user_id: ID of the admin making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        if not user_ids:
            return False, "No users selected"

        # Handle different action types
        if action == 'delete':
            # Handle bulk deletion
            success_count = 0
            for user_id in user_ids:
                success, _ = delete_user(user_id)
                if success:
                    success_count += 1
            
            if success_count == len(user_ids):
                return True, f"Successfully deleted {success_count} users"
            elif success_count > 0:
                return True, f"Deleted {success_count} of {len(user_ids)} users"
            else:
                return False, "Failed to delete any users"
        
        elif action == 'status':
            # Update account status
            if value not in ['active', 'pending', 'locked', 'disabled']:
                return False, "Invalid status value"
            
            # Build update query
            query = f"UPDATE users SET account_status = ? WHERE user_id IN ({','.join(['?' for _ in user_ids])})"
            params = [value] + user_ids
            db.execute_update(query, tuple(params))
            
            # Log the bulk action
            if admin_user_id:
                log_user_activity(
                    user_id=admin_user_id,
                    action_type="bulk_status_update",
                    details=f"Bulk updated {len(user_ids)} users to status: {value}",
                    status="success"
                )
            
            return True, f"Updated {len(user_ids)} users to {value} status"
        
        elif action == 'role':
            # Update role and potentially assign to default group
            if value not in ['admin', 'editor', 'viewer']:
                return False, "Invalid role value"
            
            # Start with role update
            query = f"UPDATE users SET role = ? WHERE user_id IN ({','.join(['?' for _ in user_ids])})"
            params = [value] + user_ids
            db.execute_update(query, tuple(params))
            
            # For editor and viewer roles, assign to default groups
            if value in ['editor', 'viewer']:
                group_name = f"{value.capitalize()} Group"
                
                # Get or create the default group
                group_data = db.execute_query(
                    "SELECT group_id FROM permission_groups WHERE name = ?",
                    (group_name,)
                )
                
                if group_data:
                    group_id = group_data[0]['group_id']
                else:
                    # Create the group if it doesn't exist
                    db.execute_insert(
                        "INSERT INTO permission_groups (name, description) VALUES (?, ?)",
                        (group_name, f"Default group for {value} users")
                    )
                    group_id = db.execute_query(
                        "SELECT last_insert_rowid() as id"
                    )[0]['id']
                
                # Assign users to the group
                group_query = f"UPDATE users SET group_id = ? WHERE user_id IN ({','.join(['?' for _ in user_ids])})"
                group_params = [group_id] + user_ids
                db.execute_update(group_query, tuple(group_params))
            
            # Log the bulk action
            if admin_user_id:
                log_user_activity(
                    user_id=admin_user_id,
                    action_type="bulk_role_update",
                    details=f"Bulk updated {len(user_ids)} users to role: {value}",
                    status="success"
                )
            
            return True, f"Updated {len(user_ids)} users to {value} role"
        
        else:
            return False, f"Unknown action: {action}"
            
    except Exception as e:
        logger.error(f"Failed to bulk update users: {str(e)}")
        return False, f"Failed to bulk update users: {str(e)}"


def update_user_permissions(user_id: int, category: str, permission: str) -> Tuple[bool, str]:
    """
    Update user permissions.

    Args:
        user_id: The user ID
        category: The permission category
        permission: The permission to update

    Returns:
        A tuple of (success, message)
    """
    try:
        # This function would update specific user permissions
        # Implementation depends on your permission system structure
        return True, "Permission updated successfully"
    except Exception as e:
        logger.error(f"Failed to update user permissions: {str(e)}")
        return False, f"Failed to update user permissions: {str(e)}"


def update_dashboard_permission(user_id: int, function_name: str, enabled: bool) -> Tuple[bool, str]:
    """
    Update user's dashboard permission.

    Args:
        user_id: The user ID
        function_name: The function name
        enabled: Whether the permission is enabled

    Returns:
        A tuple of (success, message)
    """
    try:
        # Check if permission already exists
        existing = db.execute_query(
            "SELECT * FROM dashboard_permissions WHERE user_id = ? AND function_name = ?",
            (user_id, function_name)
        )

        if existing:
            # Update existing permission
            db.execute_update(
                "UPDATE dashboard_permissions SET enabled = ? WHERE user_id = ? AND function_name = ?",
                (enabled, user_id, function_name)
            )
        else:
            # Insert new permission
            db.execute_insert(
                "INSERT INTO dashboard_permissions (user_id, function_name, enabled) VALUES (?, ?, ?)",
                (user_id, function_name, enabled)
            )

        return True, "Dashboard permission updated successfully"
    except Exception as e:
        logger.error(f"Failed to update dashboard permission: {str(e)}")
        return False, f"Failed to update dashboard permission: {str(e)}"


def update_permission_override(user_id: int, function_name: str, enabled: bool, admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update permission override for user.

    Args:
        user_id: The user ID
        function_name: The function name
        enabled: Whether the permission is enabled
        admin_user_id: ID of the admin making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        # Check if override already exists
        existing = db.execute_query(
            "SELECT * FROM permission_overrides WHERE user_id = ? AND function_name = ?",
            (user_id, function_name)
        )

        if existing:
            # Update existing override
            db.execute_update(
                "UPDATE permission_overrides SET enabled = ? WHERE user_id = ? AND function_name = ?",
                (enabled, user_id, function_name)
            )
        else:
            # Insert new override
            db.execute_insert(
                "INSERT INTO permission_overrides (user_id, function_name, enabled) VALUES (?, ?, ?)",
                (user_id, function_name, enabled)
            )

        # Log the permission change
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=user_id,
            change_type="permission_override",
            entity_changed=function_name,
            old_value="None",
            new_value="enabled" if enabled else "disabled"
        )

        return True, "Permission override updated successfully"
    except Exception as e:
        logger.error(f"Failed to update permission override: {str(e)}")
        return False, f"Failed to update permission override: {str(e)}"


def update_user_group(user_id: int, group_id: Optional[int], admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update user's group.

    Args:
        user_id: The user ID
        group_id: The group ID (None to remove from group)
        admin_user_id: ID of the admin making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        db.execute_update(
            "UPDATE users SET group_id = ? WHERE user_id = ?",
            (group_id, user_id)
        )

        # Log the group change
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=user_id,
            change_type="user_group_changed",
            entity_changed="user_group",
            old_value="None",
            new_value=str(group_id) if group_id else "None"
        )

        return True, "User group updated successfully"
    except Exception as e:
        logger.error(f"Failed to update user group: {str(e)}")
        return False, f"Failed to update user group: {str(e)}"


def get_permission_groups() -> List['PermissionGroup']:
    """
    Get all permission groups.

    Returns:
        List of PermissionGroup objects
    """
    try:
        groups_data = db.execute_query("SELECT * FROM permission_groups ORDER BY name")
        groups = []
        
        for group_data in groups_data:
            group = PermissionGroup(group_data)
            
            # Get count of users in this group
            user_count_data = db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE group_id = ?", 
                (group.group_id,)
            )
            group.user_count = user_count_data[0]['count'] if user_count_data else 0
            
            # Load permissions
            group.load_permissions()
            groups.append(group)
            
        return groups
    except Exception as e:
        logger.error(f"Failed to get permission groups: {str(e)}")
        return []


def update_group_permission(group_id: int, function_name: str, enabled: bool, admin_user_id: Optional[int] = None) -> Tuple[bool, str]:
    """
    Update group permission.

    Args:
        group_id: The group ID
        function_name: The function name
        enabled: Whether the permission is enabled
        admin_user_id: ID of the admin making the change

    Returns:
        A tuple of (success, message)
    """
    try:
        # Check if permission already exists
        existing = db.execute_query(
            "SELECT * FROM group_permissions WHERE group_id = ? AND function_name = ?",
            (group_id, function_name)
        )

        if existing:
            # Update existing permission
            db.execute_update(
                "UPDATE group_permissions SET enabled = ? WHERE group_id = ? AND function_name = ?",
                (enabled, group_id, function_name)
            )
        else:
            # Insert new permission
            db.execute_insert(
                "INSERT INTO group_permissions (group_id, function_name, enabled) VALUES (?, ?, ?)",
                (group_id, function_name, enabled)
            )

        # Log the permission change
        log_permission_change(
            admin_user_id=admin_user_id,
            target_user_id=None,
            change_type="group_permission_changed",
            entity_changed=f"group:{group_id}:{function_name}",
            old_value="None",
            new_value="enabled" if enabled else "disabled"
        )

        return True, "Group permission updated successfully"
    except Exception as e:
        logger.error(f"Failed to update group permission: {str(e)}")
        return False, f"Failed to update group permission: {str(e)}"


def get_user_activity_logs(page: int = 1, per_page: int = 25, user_id: Optional[int] = None,
                          action_type: Optional[str] = None, start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get user activity logs with filtering and pagination.

    Args:
        page: The page number
        per_page: The number of logs per page
        user_id: Filter by user ID
        action_type: Filter by action type
        start_date: Filter by start date
        end_date: Filter by end date

    Returns:
        A tuple of (logs, total_count)
    """
    try:
        # Build query
        query = """
            SELECT l.*, u.username, u.full_name
            FROM user_activity_logs l
            LEFT JOIN users u ON l.user_id = u.user_id
        """
        count_query = "SELECT COUNT(*) as count FROM user_activity_logs l"
        
        conditions = []
        params = []

        if user_id:
            conditions.append("l.user_id = ?")
            params.append(user_id)

        if action_type:
            conditions.append("l.action_type = ?")
            params.append(action_type)

        if start_date:
            conditions.append("l.timestamp >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("l.timestamp <= ?")
            params.append(end_date)

        if conditions:
            where_clause = " WHERE " + " AND ".join(conditions)
            query += where_clause
            count_query += where_clause

        # Add ordering and pagination
        query += " ORDER BY l.timestamp DESC LIMIT ? OFFSET ?"
        params.extend([per_page, (page - 1) * per_page])

        # Get logs
        logs = db.execute_query(query, tuple(params))

        # Get total count
        count_result = db.execute_query(count_query, tuple(params[:-2] if len(params) > 2 else []))
        total_count = count_result[0]['count'] if count_result else 0

        return logs, total_count
    except Exception as e:
        logger.error(f"Failed to get user activity logs: {str(e)}")
        return [], 0


def get_permission_audit_logs(page: int = 1, per_page: int = 25, user_id: Optional[int] = None,
                             function_name: Optional[str] = None, start_date: Optional[str] = None,
                             end_date: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get permission audit logs with filtering and pagination.

    Args:
        page: The page number
        per_page: The number of logs per page
        user_id: Filter by user ID
        function_name: Filter by function name
        start_date: Filter by start date
        end_date: Filter by end date

    Returns:
        A tuple of (logs, total_count)
    """
    try:
        # Build query
        query = """
            SELECT l.*, u.username, u.full_name
            FROM permission_audit_logs l
            LEFT JOIN users u ON l.user_id = u.user_id
        """
        count_query = "SELECT COUNT(*) as count FROM permission_audit_logs l"
        
        conditions = []
        params = []

        if user_id:
            conditions.append("l.user_id = ?")
            params.append(user_id)

        if function_name:
            conditions.append("l.function_name = ?")
            params.append(function_name)

        if start_date:
            conditions.append("l.timestamp >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("l.timestamp <= ?")
            params.append(end_date)

        if conditions:
            where_clause = " WHERE " + " AND ".join(conditions)
            query += where_clause
            count_query += where_clause

        # Add ordering and pagination
        query += " ORDER BY l.timestamp DESC LIMIT ? OFFSET ?"
        params.extend([per_page, (page - 1) * per_page])

        # Get logs
        logs = db.execute_query(query, tuple(params))

        # Get total count
        count_result = db.execute_query(count_query, tuple(params[:-2] if len(params) > 2 else []))
        total_count = count_result[0]['count'] if count_result else 0

        return logs, total_count
    except Exception as e:
        logger.error(f"Failed to get permission audit logs: {str(e)}")
        return [], 0
