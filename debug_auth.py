#!/usr/bin/env python3
"""
Debug script to check database connection and user data.
"""

import os
import sys
import sqlite3

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database():
    """Check database connection and user data."""
    
    print("Debugging database connection and user data...")
    
    # Check if user_management.db exists
    db_path = "./user_management.db"
    if os.path.exists(db_path):
        print(f"✅ Database file exists: {db_path}")
        print(f"   Size: {os.path.getsize(db_path)} bytes")
    else:
        print(f"❌ Database file not found: {db_path}")
        return
    
    # Connect to database
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\n📋 Tables in database:")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Check users table
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"\n👥 Users in database: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT user_id, username, email, role, account_status FROM users")
            users = cursor.fetchall()
            print(f"\n📊 User details:")
            for user in users:
                print(f"   - ID: {user[0]}, Username: {user[1]}, Email: {user[2]}, Role: {user[3]}, Status: {user[4]}")
        
        # Check if there are any password hashes
        cursor.execute("SELECT username, password_hash FROM users LIMIT 3")
        passwords = cursor.fetchall()
        print(f"\n🔐 Password hashes (first 3):")
        for pwd in passwords:
            hash_preview = pwd[1][:20] + "..." if pwd[1] and len(pwd[1]) > 20 else pwd[1]
            print(f"   - {pwd[0]}: {hash_preview}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database connection error: {str(e)}")

def check_environment():
    """Check environment variables and paths."""
    
    print(f"\n🔍 Environment check:")
    print(f"   Current working directory: {os.getcwd()}")
    print(f"   USER_DB_PATH env var: {os.getenv('USER_DB_PATH', 'Not set')}")
    
    # Check if user_management.db exists in current directory
    if os.path.exists("./user_management.db"):
        print(f"   ✅ user_management.db found in current directory")
    else:
        print(f"   ❌ user_management.db not found in current directory")
    
    # List files in current directory
    print(f"\n📁 Files in current directory:")
    for file in os.listdir("."):
        if file.endswith(".db"):
            size = os.path.getsize(file)
            print(f"   - {file} ({size} bytes)")

if __name__ == "__main__":
    check_environment()
    check_database()
