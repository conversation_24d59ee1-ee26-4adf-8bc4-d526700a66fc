{% extends "admin_base.html" %}

{% block title %}Upload Content{% endblock %}

{% block head %}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <style>
        /* Additional text contrast fixes specific to upload page */
        .bg-white .text-gray-800 { color: #1a202c !important; }
        .bg-white .text-gray-700 { color: #2d3748 !important; }
        .bg-white .text-gray-600 { color: #4a5568 !important; }
        .bg-white .text-gray-500 { color: #6b7280 !important; }

        /* Ensure form elements have proper contrast */
        input, select, textarea { color: #1a202c !important; }

        /* Fix for info boxes */
        .bg-blue-50 .text-blue-700 { color: #1e40af !important; }
        .bg-yellow-50 .text-yellow-800 { color: #92400e !important; }

        /* Dark mode styles */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #374151 !important; }
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* Form elements in dark mode - maintain readability */
        .dark input:not([type="radio"]):not([type="checkbox"]),
        .dark select,
        .dark textarea {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
            color: #f3f4f6 !important;
        }

        /* Info boxes in dark mode */
        .dark .bg-blue-50 .text-blue-700 { color: #bfdbfe !important; }
        .dark .bg-yellow-50 .text-yellow-800 { color: #fef3c7 !important; }

        /* Links in dark mode */
        .dark a:not(.btn) { color: #93c5fd !important; }
        .dark a:hover:not(.btn) { color: #bfdbfe !important; }

        /* Buttons in dark mode */
        .dark .bg-blue-600 { background-color: #2563eb !important; }
        .dark .bg-green-600 { background-color: #059669 !important; }
        .dark .hover\:bg-blue-700:hover { background-color: #1d4ed8 !important; }
        .dark .hover\:bg-green-700:hover { background-color: #047857 !important; }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Upload Content</h1>

            {% if not categories %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No categories found. Please create a category using the form below before uploading content.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- PDF Upload Section -->
                <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        Upload PDF Document
                    </h2>

                    <form method="POST" enctype="multipart/form-data" class="space-y-4">
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div>
                            <label for="category_pdf" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select name="category" id="category_pdf" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" {% if not categories %}disabled{% endif %}>
                                <option value="" disabled selected>-- Select Category --</option>
                                {% for category in categories %}
                                    <option value="{{ category }}">{{ category }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="file" class="block text-sm font-medium text-gray-700 mb-1">PDF File</label>
                            <div class="relative">
                                <input type="file" name="file" id="file" accept=".pdf" multiple
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       {% if not categories %}disabled{% endif %}>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Maximum file size: 25MB</p>
                        </div>
                        <div id="file-list" class="mt-2 text-xs text-gray-600"></div>
                        <div id="upload-progress-list" class="mt-2"></div>

                        <div>
                            <label for="pdf_url" class="block text-sm font-medium text-gray-700 mb-1">Source URL (Optional)</label>
                            <input type="url" name="pdf_url" id="pdf_url" placeholder="https://example.com/document-source"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   {% if not categories %}disabled{% endif %}>
                            <p class="mt-1 text-xs text-gray-500">URL where this PDF can be found online</p>
                        </div>

                        <!-- Gated Download Form Selection -->
                        <div>
                            <label for="gated_form_id" class="block text-sm font-medium text-gray-700 mb-1">Gated Download Form (Optional)</label>
                            <select name="gated_form_id" id="gated_form_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">-- No Form (Public Access) --</option>
                                {% for form in forms %}
                                    <option value="{{ form.id }}" {% if selected_form_id and selected_form_id|int == form.id %}selected{% endif %}>{{ form.name }}</option>
                                {% endfor %}
                            </select>
                            <p class="mt-1 text-xs text-gray-500">If selected, users must fill out this form before downloading the PDF.</p>
                            <p class="mt-1 text-xs text-blue-600 font-medium">💡 Select a form above to see OCR conversion options</p>
                            <button type="button" id="test-ocr-options" class="mt-2 px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300" onclick="document.getElementById('ocr-conversion-options').style.display='block'">
                                🧪 Show OCR Options (Test Mode)
                            </button>
                        </div>

                        <div id="ocr-conversion-options" class="border border-gray-200 rounded-md p-3 bg-gray-50 mt-4" style="display: none;">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">OCR Conversion Options</h3>
                            <div class="flex items-center">
                                <input type="checkbox" id="convert_to_non_ocr" name="convert_to_non_ocr" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <label for="convert_to_non_ocr" class="ml-2 text-sm text-gray-700">Convert to non-OCR PDF for download</label>
                            </div>
                            <div id="conversion-quality-settings" class="mt-2" style="display: none;">
                                <label for="conversion_dpi" class="block text-xs font-medium text-gray-700 mb-1">Conversion Quality (DPI)</label>
                                <select name="conversion_dpi" id="conversion_dpi" class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                                    <option value="150">150 DPI (Fastest)</option>
                                    <option value="300" selected>300 DPI (Recommended)</option>
                                    <option value="600">600 DPI (Highest Quality)</option>
                                </select>
                            </div>
                            <div id="ocr-options" class="mt-2" style="display: none;">
                                <div class="flex items-center">
                                    <input type="checkbox" id="keep_only_non_ocr" name="keep_only_non_ocr" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="keep_only_non_ocr" class="ml-2 text-sm text-gray-700">Delete original OCR version after conversion</label>
                                </div>
                            </div>
                        </div>

                        <!-- Smart PDF Processing (Unified) -->
                        <div class="border border-gray-200 rounded-md p-3 bg-blue-50 mb-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <svg class="h-4 w-4 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                Smart PDF Processing
                            </h3>
                            
                            <!-- Main Toggle -->
                            <div class="mb-3">
                                <div class="flex items-center">
                                    <input type="checkbox" id="smart_processing" name="smart_processing" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <label for="smart_processing" class="ml-2 text-sm text-gray-700 font-medium">Enable smart processing for all PDFs</label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">
                                    Automatically detects file types and applies optimal processing strategies. OCR files are converted to clean downloads while preserving search functionality.
                                </p>
                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                                    <strong>Debug Info:</strong> When enabled, this will automatically convert OCR PDFs to non-OCR for clean downloads and apply dual conversion for non-OCR PDFs.
                                </div>
                                <div class="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-800">
                                    <strong>Troubleshooting:</strong> If conversion is not working, try the debug script: <code>python debug_ocr_conversion.py</code>
                                </div>
                            </div>
                            
                            <!-- Advanced Options (collapsible) -->
                            <div id="advanced_options" class="mt-3" style="display: none;">
                                <details class="border border-gray-200 rounded-md p-2">
                                    <summary class="text-sm font-medium text-gray-700 cursor-pointer hover:text-blue-600">
                                        Advanced Settings
                                    </summary>
                                    <div class="mt-3 space-y-3">
                                        <div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="preserve_ocr_for_search" name="preserve_ocr_for_search" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500" disabled checked>
                                                <label for="preserve_ocr_for_search" class="ml-2 text-sm text-gray-500">Search functionality uses ChromaDB (text already embedded)</label>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">
                                                Original OCR files are automatically deleted after conversion. Search uses embedded text in ChromaDB.
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="use_vision" name="use_vision" value="true" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="use_vision" class="ml-2 text-sm text-gray-700">Enable AI vision analysis for non-OCR files</label>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">
                                                Use AI vision models to analyze image content in non-OCR PDFs for better understanding.
                                            </p>
                                        </div>
                                        
                                        <div>
                                            <label for="target_dpi" class="block text-sm font-medium text-gray-700 mb-1">Image Quality (DPI)</label>
                                            <select name="target_dpi" id="target_dpi" class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                                                <option value="75">75 DPI (Smallest files, fastest download)</option>
                                                <option value="150">150 DPI (Smaller files, faster download)</option>
                                                <option value="300" selected>300 DPI (Standard quality, recommended)</option>
                                                <option value="600">600 DPI (High quality, larger file)</option>
                                            </select>
                                            <p class="mt-1 text-xs text-gray-500">
                                                Higher DPI means better quality but larger file size. 300 DPI is recommended for most documents.
                                            </p>
                                        </div>
                                    </div>
                                </details>
                            </div>
                        </div>
                        <script>
                        // Show/hide advanced options based on smart processing checkbox
                        document.addEventListener('DOMContentLoaded', function() {
                            var smartProcessingCheckbox = document.getElementById('smart_processing');
                            var advancedOptions = document.getElementById('advanced_options');
                            
                            if (smartProcessingCheckbox && advancedOptions) {
                                smartProcessingCheckbox.addEventListener('change', function() {
                                    advancedOptions.style.display = this.checked ? 'block' : 'none';
                                });
                                
                                // Show advanced options if smart processing is checked by default
                                if (smartProcessingCheckbox.checked) {
                                    advancedOptions.style.display = 'block';
                                }
                            }
                        });
                        </script>

                        <!-- Duplicate Handling Options -->
                        <div class="border border-gray-200 rounded-md p-3 bg-gray-50">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Duplicate Handling</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="radio" id="duplicate_reject" name="duplicate_action" value="reject" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <label for="duplicate_reject" class="ml-2 text-sm text-gray-700">Reject duplicate uploads</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="duplicate_replace" name="duplicate_action" value="replace" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="duplicate_replace" class="ml-2 text-sm text-gray-700">Replace existing file</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="radio" id="duplicate_allow" name="duplicate_action" value="allow" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="duplicate_allow" class="ml-2 text-sm text-gray-700">Allow duplicates</label>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Duplicates are detected by filename or content hash</p>
                            </div>
                        </div>

                        <!-- Vision Model Options -->
                        <div class="border border-gray-200 rounded-md p-3 bg-gray-50">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Vision Model Options</h3>

                            <div class="mb-3 text-xs text-gray-600">
                                <p>Vision model usage is controlled by global settings in the <a href="{{ url_for('manage_models') }}" class="text-blue-600 hover:underline">Models</a> page.</p>
                                <div id="vision_status_message" class="mt-1 font-medium"></div>
                            </div>

                            <!-- Filter Sensitivity -->
                            <div class="mb-3">
                                <label class="block text-xs font-medium text-gray-700 mb-1">Filter Sensitivity</label>
                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_low" name="filter_sensitivity" value="low" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <label for="sensitivity_low" class="ml-1 text-xs text-gray-700">Low</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_medium" name="filter_sensitivity" value="medium" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                        <label for="sensitivity_medium" class="ml-1 text-xs text-gray-700">Medium</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="sensitivity_high" name="filter_sensitivity" value="high" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <label for="sensitivity_high" class="ml-1 text-xs text-gray-700">High</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Max Images -->
                            <div>
                                <label for="max_images" class="block text-xs font-medium text-gray-700 mb-1">Maximum Images</label>
                                <input type="number" id="max_images" name="max_images" min="1" max="50" value="10"
                                       class="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <span class="text-xs text-gray-500 ml-2">Limit images to analyze (1-50)</span>
                            </div>
                        </div>

                        <button type="submit" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" {% if not categories %}disabled{% endif %}>
                            Upload PDF
                        </button>
                    </form>
                </div>

                <!-- URL Scraping Section -->
                <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        Scrape Website URL
                    </h2>

                    <form method="POST" class="space-y-4">
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div>
                            <label for="category_url" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select name="category" id="category_url" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" {% if not categories %}disabled{% endif %}>
                                <option value="" disabled selected>-- Select Category --</option>
                                {% for category in categories %}
                                    <option value="{{ category }}">{{ category }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="url" class="block text-sm font-medium text-gray-700 mb-1">Website URL</label>
                            <input type="url" name="url" id="url" placeholder="https://example.com"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   {% if not categories %}disabled{% endif %}>
                            <p class="mt-1 text-xs text-gray-500">Enter a complete URL including http:// or https://</p>
                        </div>

                        <div>
                            <label for="depth" class="block text-sm font-medium text-gray-700 mb-1">Scraping Depth</label>
                            <input type="number" name="depth" id="depth" min="0" max="3" value="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   {% if not categories %}disabled{% endif %}>
                            <p class="mt-1 text-xs text-gray-500">0 = current page only, 1-3 = follow links to specified depth</p>
                        </div>

                        <div class="mt-2 flex items-center">
                            <input type="checkbox" id="force_update_url" name="force_update" value="true" class="h-4 w-4 text-green-600 focus:ring-green-500">
                            <label for="force_update_url" class="ml-2 text-sm text-gray-700">Force update URL content</label>
                            <div class="ml-1 group relative">
                                <svg class="h-4 w-4 text-gray-400 cursor-help" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                </svg>
                                <div class="opacity-0 group-hover:opacity-100 absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg transition-opacity duration-200 bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2">
                                    By default, URL content is cached for 7 days. Check this to force a fresh scrape of the URL content.
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors" {% if not categories %}disabled{% endif %}>
                            Scrape URL
                        </button>
                    </form>
                </div>
            </div>

            <div class="mt-6 bg-blue-50 border-l-4 border-blue-500 p-4 rounded">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            Uploaded content will be processed, vectorized, and made available for chat. You can view and manage all uploaded content in the <a href="{{ url_for('list_files') }}" class="font-medium underline">Files section</a>.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Management Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <svg class="h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                Manage Categories
            </h2>

            <div class="mb-4">
                <div class="flex space-x-2">
                    <input type="text" id="newCategory" placeholder="New category name"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button onclick="createCategory()"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        Create
                    </button>
                </div>
                <p class="mt-2 text-sm text-gray-600">Categories are used to organize your knowledge base. Create categories for different topics or document types.</p>
            </div>

            {% if categories %}
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="py-3 px-4 text-left text-gray-600 font-medium">Category Name</th>
                                <th class="py-3 px-4 text-right text-gray-600 font-medium">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                                <tr class="border-t border-gray-200 hover:bg-gray-50">
                                    <td class="py-3 px-4 font-medium text-gray-800">{{ category }}</td>
                                    <td class="py-3 px-4 text-right">
                                        <button onclick="deleteCategory('{{ category }}')"
                                                class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
                                                title="Delete this category and all its content">
                                            <i class="fas fa-trash mr-1"></i>Delete
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No categories found. Create a category to get started.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="/static/admin.js"></script>
    <script>
        // Display vision model status and handle duplicate detection
        document.addEventListener('DOMContentLoaded', function() {
            const visionStatusMessage = document.getElementById('vision_status_message');
            const sensitivityInputs = document.querySelectorAll('input[name="filter_sensitivity"]');
            const maxImagesInput = document.getElementById('max_images');

            // PDF upload form elements
            const fileInput = document.getElementById('file');
            const categorySelect = document.getElementById('category_pdf');
            const pdfForm = fileInput.closest('form');
            const submitButton = pdfForm.querySelector('button[type="submit"]');

            // Create a div for duplicate warnings
            const duplicateWarningDiv = document.createElement('div');
            duplicateWarningDiv.className = 'mt-2 hidden';
            fileInput.parentNode.appendChild(duplicateWarningDiv);

            // Function to check the global vision embedding setting
            function checkGlobalVisionSetting() {
                fetch('/api/settings/vision_embedding_status')
                    .then(response => response.json())
                    .then(data => {
                        if (!data.enabled) {
                            // If vision model during embedding is disabled globally
                            visionStatusMessage.textContent = 'Vision model is currently disabled globally.';
                            visionStatusMessage.className = 'mt-1 font-medium text-red-600 dark:text-red-400';

                            // Disable sensitivity and max images inputs
                            sensitivityInputs.forEach(input => {
                                input.disabled = true;
                            });
                            maxImagesInput.disabled = true;
                        } else {
                            // If vision model during embedding is enabled globally
                            visionStatusMessage.textContent = 'Vision model is currently enabled globally.';
                            visionStatusMessage.className = 'mt-1 font-medium text-green-600 dark:text-green-400';

                            // Enable sensitivity and max images inputs
                            sensitivityInputs.forEach(input => {
                                input.disabled = false;
                            });
                            maxImagesInput.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching vision embedding setting:', error);
                        visionStatusMessage.textContent = 'Unable to determine vision model status.';
                        visionStatusMessage.className = 'mt-1 font-medium text-gray-600 dark:text-gray-400';
                    });
            }

            // Function to get CSRF token from meta tag
            function getCSRFToken() {
                return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            }

            // Function to check for duplicate PDFs
            function checkForDuplicates() {
                // Only check if both file and category are selected
                if (!fileInput.files.length || !categorySelect.value) {
                    duplicateWarningDiv.classList.add('hidden');
                    return;
                }

                const file = fileInput.files[0];
                const category = categorySelect.value;

                // Create form data
                const formData = new FormData();
                formData.append('file', file);
                formData.append('category', category);

                // Show loading state
                duplicateWarningDiv.innerHTML = '<p class="text-sm text-gray-600 dark:text-gray-400">Checking for duplicates...</p>';
                duplicateWarningDiv.classList.remove('hidden');

                // Send request to check for duplicates
                fetch('/api/check_duplicate', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': getCSRFToken()
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        // Show error message
                        duplicateWarningDiv.innerHTML = `
                            <div class="p-2 bg-red-50 dark:bg-red-900 border-l-4 border-red-400 text-red-800 dark:text-red-200 text-sm">
                                <p><strong>Error:</strong> ${data.error}</p>
                            </div>
                        `;
                        duplicateWarningDiv.classList.remove('hidden');
                    } else if (data.is_duplicate) {
                        // Show duplicate warning
                        const message = data.message || 'A duplicate file was detected.';
                        duplicateWarningDiv.innerHTML = `
                            <div class="p-2 bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 text-yellow-800 dark:text-yellow-200 text-sm">
                                <p><strong>Duplicate detected:</strong> ${message}</p>
                                <p class="text-xs mt-1">Use the "Duplicate Handling" options below to decide how to proceed.</p>
                            </div>
                        `;
                        duplicateWarningDiv.classList.remove('hidden');
                    } else {
                        // Hide warning if no duplicate
                        duplicateWarningDiv.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error checking for duplicates:', error);
                    duplicateWarningDiv.innerHTML = `
                        <div class="p-2 bg-red-50 dark:bg-red-900 border-l-4 border-red-400 text-red-800 dark:text-red-200 text-sm">
                            <p><strong>Error:</strong> ${error.message}</p>
                            <p class="text-xs mt-1">Please try again or contact support if the problem persists.</p>
                        </div>
                    `;
                    duplicateWarningDiv.classList.remove('hidden');
                });
            }

            // Add event listeners for duplicate detection
            fileInput.addEventListener('change', checkForDuplicates);
            categorySelect.addEventListener('change', checkForDuplicates);

            // Check global setting when page loads
            checkGlobalVisionSetting();

            // Listen for theme changes and update UI accordingly
            document.addEventListener('themeChanged', function(e) {
                const isDark = e.detail.isDark;
                console.log('Theme changed to:', isDark ? 'dark' : 'light');

                // Re-check duplicates to update warning colors if visible
                if (!duplicateWarningDiv.classList.contains('hidden')) {
                    checkForDuplicates();
                }

                // Re-check vision status to update text colors
                checkGlobalVisionSetting();
            });
        });

        // OCR Conversion Options Management
        document.addEventListener('DOMContentLoaded', function() {
            const gatedFormSelect = document.getElementById('gated_form_id');
            const ocrConversionOptions = document.getElementById('ocr-conversion-options');
            const convertToNonOcrCheckbox = document.getElementById('convert_to_non_ocr');
            const conversionQualitySettings = document.getElementById('conversion-quality-settings');
            const ocrOptions = document.getElementById('ocr-options');

            // Function to toggle OCR conversion options visibility
            function toggleOcrConversionOptions() {
                const isGatedUpload = gatedFormSelect.value !== '';
                
                if (isGatedUpload) {
                    ocrConversionOptions.style.display = 'block';
                } else {
                    ocrConversionOptions.style.display = 'none';
                    // Reset checkbox when hiding
                    convertToNonOcrCheckbox.checked = false;
                    conversionQualitySettings.style.display = 'none';
                    ocrOptions.style.display = 'none';
                }
            }

            // Function to toggle quality settings and OCR options visibility
            function toggleQualitySettings() {
                if (convertToNonOcrCheckbox.checked) {
                    conversionQualitySettings.style.display = 'block';
                    ocrOptions.style.display = 'block';
                } else {
                    conversionQualitySettings.style.display = 'none';
                    ocrOptions.style.display = 'none';
                    // Reset the keep only non-OCR checkbox when conversion is disabled
                    const keepOnlyCheckbox = document.getElementById('keep_only_non_ocr');
                    if (keepOnlyCheckbox) {
                        keepOnlyCheckbox.checked = false;
                    }
                }
            }

            // Add event listeners
            gatedFormSelect.addEventListener('change', toggleOcrConversionOptions);
            convertToNonOcrCheckbox.addEventListener('change', toggleQualitySettings);

            // Initialize visibility on page load
            toggleOcrConversionOptions();
        });

        // Multiple file selection display
        document.addEventListener('DOMContentLoaded', function() {
            // --- DOM Elements ---
            const categorySelect = document.getElementById('category_pdf');
            const fileInput = document.getElementById('file');
            const fileListDiv = document.getElementById('file-list');
            const progressListDiv = document.getElementById('upload-progress-list');
            if (!categorySelect || !fileInput || !fileListDiv || !progressListDiv) {
                console.error('[BatchDuplicateCheck] One or more required DOM elements are missing:', {
                    categorySelect, fileInput, fileListDiv, progressListDiv
                });
                return;
            }
            // --- Batch Duplicate Check Logic (Refactored with Debug Logging) ---
            async function fetchExistingFilenames(category) {
                if (!category) return [];
                try {
                    console.log(`[FrontendDuplicateCheck] Fetching files for category: ${category}`);
                    const res = await fetch(`/api/files?category=${encodeURIComponent(category)}`);
                    if (!res.ok) {
                        console.error(`[FrontendDuplicateCheck] API request failed: ${res.status} - ${res.statusText}`);
                        return [];
                    }
                    const files = await res.json();
                    console.log(`[FrontendDuplicateCheck] Raw API response:`, files);
                    
                    let result = [];
                    if (Array.isArray(files)) {
                        result = files.map(f => (typeof f === 'string' ? f : f.filename || f.original_filename)).filter(Boolean);
                    } else if (files.files && Array.isArray(files.files)) {
                        result = files.files.map(f => (typeof f === 'string' ? f : f.filename || f.original_filename)).filter(Boolean);
                    }
                    
                    console.log(`[FrontendDuplicateCheck] Processed filenames:`, result);
                    return result;
                } catch (e) {
                    console.error('[FrontendDuplicateCheck] Error fetching existing filenames:', e);
                    return [];
                }
            }

            async function checkBatchDuplicates() {
                const category = categorySelect.value;
                const files = fileInput.files;
                if (!category || !files.length) {
                    fileListDiv.innerHTML = '';
                    return;
                }
                const existingFilenames = (await fetchExistingFilenames(category)).map(f => f.toLowerCase());
                console.log('[BatchDuplicateCheck] Existing filenames:', existingFilenames);
                let html = '';
                let hasDuplicates = false;
                if (files.length > 1) {
                    html = '<strong>Selected files:</strong><ul>';
                    for (let i = 0; i < files.length; i++) {
                        const fname = files[i].name;
                        const isDup = existingFilenames.includes(fname.toLowerCase());
                        console.log(`[BatchDuplicateCheck] File ${i}: ${fname} - Duplicate: ${isDup}`);
                        if (isDup) {
                            html += `<li style='color: orange'><strong>${fname}</strong> <span style='color: orange'>(Duplicate: already exists in this category)</span></li>`;
                            hasDuplicates = true;
                        } else {
                            html += `<li>${fname}</li>`;
                        }
                    }
                    html += '</ul>';
                } else if (files.length === 1) {
                    const fname = files[0].name;
                    const isDup = existingFilenames.includes(fname.toLowerCase());
                    console.log(`[BatchDuplicateCheck] Single file: ${fname} - Duplicate: ${isDup}`);
                    if (isDup) {
                        html = `<strong>Selected file:</strong> <span style='color: orange'>${fname} (Duplicate: already exists in this category)</span>`;
                        hasDuplicates = true;
                    } else {
                        html = `<strong>Selected file:</strong> ${fname}`;
                    }
                }
                fileListDiv.innerHTML = html;
                progressListDiv.innerHTML = '';
            }
            // --- End Batch Duplicate Check Logic ---

            // Remove all previous file/category change event listeners (if any)
            fileInput.onchange = null;
            categorySelect.onchange = null;
            // Add only the batch duplicate check as the event handler
            fileInput.addEventListener('change', checkBatchDuplicates);
            categorySelect.addEventListener('change', checkBatchDuplicates);
        });

        // Add modal HTML for upload progress
        const uploadModalHtml = `
        <div id="uploadProgressModal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
            <div class="bg-white dark:bg-gray-100 rounded-lg shadow-lg p-6 w-full max-w-md border border-gray-300 dark:border-gray-400">
                <h2 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-900">Uploading Files</h2>
                <ul id="modal-file-list" class="mb-4"></ul>
                <button id="closeUploadModal" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Close</button>
            </div>
            <div class="fixed inset-0 bg-black opacity-30"></div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', uploadModalHtml);
        const uploadModal = document.getElementById('uploadProgressModal');
        const modalFileList = document.getElementById('modal-file-list');
        const closeUploadModal = document.getElementById('closeUploadModal');
        closeUploadModal.addEventListener('click', () => {
            uploadModal.classList.add('hidden');
        });

        // Intercept form submit for all files (single and multiple)
        document.addEventListener('DOMContentLoaded', function() {
            const pdfForm = document.getElementById('file').closest('form');
            if (!pdfForm) {
                console.error('PDF form not found.');
                return;
            }
            pdfForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                const category = document.getElementById('category_pdf').value;
                if (!category) {
                    alert('Please select a category.');
                    return;
                }
                if (document.getElementById('file').files.length === 0) {
                    alert('Please select at least one PDF file.');
                    return;
                }
                // Show modal and initialize file list
                modalFileList.innerHTML = '';
                for (let i = 0; i < document.getElementById('file').files.length; i++) {
                    const file = document.getElementById('file').files[i];
                    modalFileList.innerHTML += `<li id="modal-file-status-${i}"><strong>${file.name}</strong>: <span class="text-blue-600">Pending</span></li>`;
                }
                uploadModal.classList.remove('hidden');
                let results = [];
                const fileResults = new Array(document.getElementById('file').files.length).fill(null); // Store per-file backend result
                // Parallel upload: start all uploads at once
                const uploadPromises = [];
                for (let i = 0; i < document.getElementById('file').files.length; i++) {
                    uploadPromises.push(new Promise((resolve) => {
                        const file = document.getElementById('file').files[i];
                        const statusLi = document.getElementById(`modal-file-status-${i}`);
                        const statusSpan = statusLi.querySelector('span');
                        statusSpan.textContent = 'Uploading...';
                        statusSpan.className = 'text-blue-600';
                        const formData = new FormData(pdfForm);
                        formData.set('file', file); // Only one file per request
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', window.location.pathname, true);
                        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                        xhr.upload.onprogress = function (e) {
                            if (e.lengthComputable) {
                                // Show only 'Uploading...' (no percent)
                                statusSpan.textContent = `Uploading...`;
                            }
                        };
                        xhr.onload = function () {
                            let resp;
                            try {
                                resp = JSON.parse(xhr.responseText);
                            } catch (e) {
                                resp = null;
                            }
                            let fileResult = null;
                            if (resp && resp.results && Array.isArray(resp.results)) {
                                // Try to match by file name or original_filename
                                fileResult = resp.results.find(r => r.file === file.name || r.original_filename === file.name);
                                // Fallback: try by system_filename
                                if (!fileResult) fileResult = resp.results.find(r => r.system_filename === file.name);
                                // Fallback: try by input_index
                                if (!fileResult) fileResult = resp.results.find(r => r.input_index === i);
                            }
                            fileResults[i] = fileResult; // Store the result for this file
                            if (xhr.status === 200 && fileResult && fileResult.success) {
                                statusSpan.textContent = 'Uploaded';
                                statusSpan.className = 'text-green-600';
                                results.push(file.name + ': Uploaded successfully.');
                            } else {
                                let errorMsg = (fileResult && fileResult.message) ? fileResult.message : 'Failed';
                                statusSpan.textContent = errorMsg;
                                statusSpan.className = 'text-red-600';
                                results.push(file.name + ': ' + errorMsg);
                            }
                            resolve();
                        };
                        xhr.onerror = function () {
                            statusSpan.textContent = 'Error';
                            statusSpan.className = 'text-red-600';
                            results.push(file.name + ': Error uploading file.');
                            fileResults[i] = null;
                            resolve();
                        };
                        xhr.send(formData);
                    }));
                }
                // Wait for all uploads to finish
                await Promise.all(uploadPromises);
                // Show results in modal and as toast
                let alertHtml = '<ul>';
                let hasWarning = false;
                for (let i = 0; i < fileResults.length; i++) {
                    const fileResult = fileResults[i];
                    if (fileResult && fileResult.message) {
                        let msg = fileResult.message;
                        let color = 'black';
                        if (msg.toLowerCase().includes('duplicate')) {
                            color = 'orange';
                            hasWarning = true;
                        } else if (msg.toLowerCase().includes('uploaded')) {
                            color = 'green';
                        } else if (msg.toLowerCase().includes('error') || msg.toLowerCase().includes('fail')) {
                            color = 'red';
                        }
                        alertHtml += `<li style=\"color:${color}\"><strong>${document.getElementById('file').files[i].name}</strong>: ${msg}</li>`;
                    }
                }
                alertHtml += '</ul>';
                modalFileList.innerHTML = alertHtml;
                if (hasWarning && window.DMSUtils && DMSUtils.showToast) {
                    DMSUtils.showToast('Some files were duplicates and replaced or rejected. See details.', 'warning', 6000);
                }
                setTimeout(() => {
                    alert(results.join('\n'));
                }, 100);
                window.location.reload();
            });
        });

        // Category management functions
        async function createCategory() {
            const input = document.getElementById('newCategory');
            const categoryName = input.value.trim();
            
            if (!categoryName) {
                showToast('Please enter a category name', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ category_name: categoryName })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showToast(data.message, 'success');
                    input.value = '';
                    // Reload the page to update the category list
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast(data.error || 'Failed to create category', 'error');
                }
            } catch (error) {
                console.error('Error creating category:', error);
                showToast('Error creating category', 'error');
            }
        }
        
        async function deleteCategory(categoryName) {
            if (!confirm(`Are you sure you want to delete the category "${categoryName}"? This will also delete all files and content in this category.`)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/categories/${encodeURIComponent(categoryName)}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showToast(data.message, 'success');
                    // Reload the page to update the category list
                    setTimeout(() => location.reload(), 1000);
                } else {
                    // Check if deletion was prevented due to related data
                    if (response.status === 409 && data.has_related_data) {
                        // Build detailed error message
                        const dataSummary = data.data_summary || {};
                        const errorDetails = [];
                        
                        if (dataSummary.pdf_documents > 0) {
                            errorDetails.push(`${dataSummary.pdf_documents} PDF document(s)`);
                        }
                        if (dataSummary.scraped_pages > 0) {
                            errorDetails.push(`${dataSummary.scraped_pages} scraped page(s)`);
                        }
                        if (dataSummary.chat_history > 0) {
                            errorDetails.push(`${dataSummary.chat_history} chat history record(s)`);
                        }
                        if (dataSummary.chat_analytics > 0) {
                            errorDetails.push(`${dataSummary.chat_analytics} analytics record(s)`);
                        }
                        if (dataSummary.content_sources > 0) {
                            errorDetails.push(`${dataSummary.content_sources} content source(s)`);
                        }
                        if (dataSummary.vector_embeddings > 0) {
                            errorDetails.push(`${dataSummary.vector_embeddings} vector embedding(s)`);
                        }
                        if (dataSummary.filesystem_files > 0) {
                            errorDetails.push(`${dataSummary.filesystem_files} file(s) in filesystem`);
                        }
                        if (dataSummary.legacy_filesystem_files > 0) {
                            errorDetails.push(`${dataSummary.legacy_filesystem_files} legacy file(s)`);
                        }
                        
                        const detailedMessage = `Cannot delete category "${categoryName}" because it contains: ${errorDetails.join(', ')}. Please delete all related data first.`;
                        showToast(detailedMessage, 'error');
                    } else {
                        showToast(data.error || 'Failed to delete category', 'error');
                    }
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                showToast('Error deleting category', 'error');
            }
        }
    </script>
{% endblock %}