#!/usr/bin/env python3
"""
Cleanup script for orphaned vector embeddings in the unified ChromaDB.
This script identifies and removes vector embeddings for files that no longer exist on disk.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.services.unified_vector_db import get_unified_vector_db
from app.utils.helpers import list_categories
import chromadb

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cleanup_orphaned_vectors.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_existing_files():
    """Get a set of all existing PDF files organized by category."""
    existing_files = {}
    temp_folder = os.getenv("TEMP_FOLDER", "./data/temp")
    
    categories = list_categories()
    
    for category in categories:
        existing_files[category] = set()
        
        # Check both possible directory structures
        category_paths = [
            os.path.join(temp_folder, category),
            os.path.join(temp_folder, "_temp", category)
        ]
        
        for category_path in category_paths:
            if not os.path.exists(category_path):
                continue
                
            logger.info(f"Scanning category path: {category_path}")
            
            # Check for files in flat structure
            for item in os.listdir(category_path):
                item_path = os.path.join(category_path, item)
                
                if os.path.isfile(item_path) and item.lower().endswith('.pdf'):
                    existing_files[category].add(item)
                    logger.debug(f"Found file: {item}")
                
                # Check for files in hierarchical structure (subdirectories)
                elif os.path.isdir(item_path):
                    try:
                        for subitem in os.listdir(item_path):
                            if subitem.lower().endswith('.pdf'):
                                existing_files[category].add(subitem)
                                logger.debug(f"Found file in subdirectory: {subitem}")
                    except PermissionError:
                        logger.warning(f"Permission denied accessing {item_path}")
                        continue
    
    return existing_files

def get_vector_documents():
    """Get all documents from the unified vector database."""
    try:
        # Access ChromaDB directly for comprehensive document listing
        unified_chroma_path = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
        client = chromadb.PersistentClient(path=unified_chroma_path)
        
        try:
            collection = client.get_collection(name="unified_collection")
        except Exception as e:
            logger.error(f"Could not access unified collection: {str(e)}")
            return {}
        
        # Get all documents with metadata
        results = collection.get(include=['metadatas'])
        
        vector_documents = {}
        
        for i, metadata in enumerate(results.get('metadatas', [])):
            if metadata and 'category' in metadata and 'source' in metadata:
                category = metadata['category']
                source = metadata['source']
                
                if category not in vector_documents:
                    vector_documents[category] = set()
                
                vector_documents[category].add(source)
                logger.debug(f"Found vector document: {source} in category: {category}")
        
        return vector_documents
        
    except Exception as e:
        logger.error(f"Failed to get vector documents: {str(e)}")
        return {}

def cleanup_orphaned_vectors(dry_run=True):
    """
    Clean up orphaned vector embeddings.
    
    Args:
        dry_run (bool): If True, only report what would be deleted without actually deleting
    """
    logger.info("Starting cleanup of orphaned vector embeddings...")
    
    # Get existing files and vector documents
    existing_files = get_existing_files()
    vector_documents = get_vector_documents()
    
    # Find orphaned vectors
    orphaned_vectors = {}
    total_orphaned = 0
    
    for category, vector_files in vector_documents.items():
        existing_in_category = existing_files.get(category, set())
        orphaned_in_category = vector_files - existing_in_category
        
        if orphaned_in_category:
            orphaned_vectors[category] = orphaned_in_category
            total_orphaned += len(orphaned_in_category)
            
            logger.info(f"Category '{category}': {len(orphaned_in_category)} orphaned vectors")
            for orphaned_file in sorted(orphaned_in_category):
                logger.info(f"  - Orphaned: {orphaned_file}")
    
    if total_orphaned == 0:
        logger.info("No orphaned vectors found. Database is clean!")
        return 0
    
    logger.info(f"Total orphaned vectors found: {total_orphaned}")
    
    if dry_run:
        logger.info("DRY RUN MODE: No vectors will be deleted. Use --delete to actually remove orphaned vectors.")
        return total_orphaned
    
    # Delete orphaned vectors
    deleted_count = 0
    try:
        # Use the unified vector database service
        from app.services.unified_vector_db import get_unified_vector_db
        unified_db = get_unified_vector_db()
        
        # Access the underlying ChromaDB collection directly
        client = unified_db._get_db()._client
        collection = client.get_collection(name="unified_collection")
        
        logger.info(f"Starting deletion of {total_orphaned} orphaned vectors...")
        
        for category, orphaned_files in orphaned_vectors.items():
            for filename in orphaned_files:
                try:
                    # Create the correct deletion filter for unified ChromaDB using operators
                    delete_filter = {
                        "$and": [
                            {"category": {"$eq": category}},
                            {"source": {"$eq": filename}}
                        ]
                    }
                    
                    # Get count before deletion for verification
                    before_count = collection.count()
                    
                    # Perform the deletion
                    collection.delete(where=delete_filter)
                    
                    # Verify deletion
                    after_count = collection.count()
                    deleted = before_count - after_count
                    
                    if deleted > 0:
                        deleted_count += deleted
                        logger.info(f"Deleted {deleted} orphaned vectors for {filename} in category {category}")
                    else:
                        logger.warning(f"No vectors found for {filename} in category {category}")
                    
                except Exception as e:
                    logger.error(f"Failed to delete vector for {filename} in {category}: {str(e)}")
        
        # Run VACUUM to reclaim space after deletions
        if deleted_count > 0:
            logger.info(f"Running VACUUM to reclaim space after deleting {deleted_count} orphaned vectors...")
            try:
                from app.utils.helpers import vacuum_chromadb
                success, size_before, size_after, space_reclaimed = vacuum_chromadb()
                if success:
                    logger.info(f"VACUUM completed successfully. Reclaimed {space_reclaimed:.2f} MB of space.")
                else:
                    logger.warning("VACUUM operation failed, but orphaned vectors were deleted.")
            except Exception as vacuum_e:
                logger.warning(f"VACUUM operation failed: {str(vacuum_e)}")
    
    except Exception as e:
        logger.error(f"Failed to initialize unified database: {str(e)}")
        return deleted_count
    
    logger.info(f"Cleanup completed. Deleted {deleted_count} orphaned vectors.")
    return deleted_count

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cleanup orphaned vector embeddings")
    parser.add_argument('--delete', action='store_true', 
                       help='Actually delete orphaned vectors (default is dry run)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        orphaned_count = cleanup_orphaned_vectors(dry_run=not args.delete)
        
        if args.delete:
            logger.info(f"Cleanup completed. Removed {orphaned_count} orphaned vectors.")
        else:
            logger.info(f"Found {orphaned_count} orphaned vectors. Use --delete to remove them.")
            
    except Exception as e:
        logger.error(f"Cleanup failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 