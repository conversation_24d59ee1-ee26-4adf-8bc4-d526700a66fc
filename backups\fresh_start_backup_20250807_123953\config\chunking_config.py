"""
Centralized Chunking Configuration for ERDB AI Application
Provides unified configuration management for text chunking strategies.
"""

import os
import json
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

@dataclass
class ChunkingConfig:
    """Centralized chunking configuration with content-type specific settings"""
    
    # Default chunking parameters
    default_chunk_size: int = 800
    default_chunk_overlap: int = 200
    semantic_threshold: float = 0.95
    max_chunk_size: int = 2000
    min_chunk_size: int = 100

    # Enhanced semantic chunking settings
    prefer_semantic_chunking: bool = True
    semantic_buffer_size: int = 1
    semantic_breakpoint_threshold: int = 95
    fallback_to_fixed_size: bool = True
    
    # Embedding configuration
    embedding_model: str = "nomic-embed-text:latest"
    ollama_base_url: str = "http://localhost:11434"
    
    # Performance settings
    batch_size: int = 10
    max_workers: int = 4
    enable_parallel_processing: bool = True
    
    # Content-type specific configurations
    content_type_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize content-type specific configurations"""
        if not self.content_type_configs:
            self.content_type_configs = {
                "technical": {
                    "chunk_size": 1000,
                    "chunk_overlap": 300,
                    "use_semantic": True,
                    "strategy": "semantic",
                    "buffer_size": 2,
                    "breakpoint_percentile_threshold": 95,
                    "prefer_semantic": True
                },
                "narrative": {
                    "chunk_size": 1200,
                    "chunk_overlap": 250,
                    "use_semantic": False,
                    "strategy": "sentence_aware",
                    "paragraph_separator": "\n\n\n",
                    "secondary_chunking_regex": "[^,.;。]+[,.;。]?"
                },
                "scientific": {
                    "chunk_size": 900,
                    "chunk_overlap": 200,
                    "use_semantic": True,
                    "strategy": "semantic",
                    "buffer_size": 1,
                    "breakpoint_percentile_threshold": 90
                },
                "general": {
                    "chunk_size": 800,
                    "chunk_overlap": 200,
                    "use_semantic": False,
                    "strategy": "sentence_aware",
                    "paragraph_separator": "\n\n",
                    "secondary_chunking_regex": "[^,.;。]+[,.;。]?"
                }
            }
    
    def get_config_for_content_type(self, content_type: str) -> Dict[str, Any]:
        """Get configuration for specific content type"""
        return self.content_type_configs.get(content_type, 
                                           self.content_type_configs["general"])
    
    @classmethod
    def from_json_file(cls, config_path: str = None) -> 'ChunkingConfig':
        """Load configuration from JSON file with fallback to defaults"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), 'default_models.json')
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            # Extract embedding parameters
            embedding_params = config_data.get('embedding_parameters', {})
            
            # Create configuration with loaded values
            config = cls(
                default_chunk_size=embedding_params.get('chunk_size', 800),
                default_chunk_overlap=embedding_params.get('chunk_overlap', 200),
                embedding_model=config_data.get('embedding_model', 'nomic-embed-text:latest'),
                batch_size=embedding_params.get('batch_size', 10),
                max_workers=embedding_params.get('processing_threads', 4)
            )
            
            logger.info(f"Loaded chunking configuration from {config_path}")
            return config
            
        except Exception as e:
            logger.warning(f"Failed to load config from {config_path}: {e}. Using defaults.")
            return cls()
    
    def update_json_file(self, config_path: str = None):
        """Update the JSON configuration file with current settings"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), 'default_models.json')
        
        try:
            # Load existing config
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            # Update embedding parameters
            config_data['embedding_parameters'].update({
                'chunk_size': self.default_chunk_size,
                'chunk_overlap': self.default_chunk_overlap,
                'batch_size': self.batch_size,
                'processing_threads': self.max_workers
            })
            
            # Update embedding model
            config_data['embedding_model'] = self.embedding_model
            
            # Write back to file
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            logger.info(f"Updated configuration file {config_path}")
            
        except Exception as e:
            logger.error(f"Failed to update config file {config_path}: {e}")

# Global configuration instance
_global_config = None

def get_chunking_config() -> ChunkingConfig:
    """Get the global chunking configuration instance"""
    global _global_config
    if _global_config is None:
        _global_config = ChunkingConfig.from_json_file()
    return _global_config

def reload_chunking_config():
    """Reload the global configuration from file"""
    global _global_config
    _global_config = ChunkingConfig.from_json_file()
    return _global_config
