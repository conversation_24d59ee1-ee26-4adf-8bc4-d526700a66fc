# ERDB System Entity-Relationship Diagram

*Generated on: 2025-08-07*

## Executive Summary

The ERDB (Environmental Research and Development Bureau) Document Management System uses a multi-database architecture with **11 databases** containing **122 tables** and **953.65 MB** of data.

## Database Architecture Overview

```mermaid
graph TB
    subgraph "Main Application Databases"
        A[erdb_main.db<br/>0.88 MB, 29 tables]
        B[chat_history.db<br/>2.82 MB, 5 tables]
        C[user_management.db<br/>0.20 MB, 8 tables]
        D[scraped_pages.db<br/>0.01 MB, 1 table]
    end
    
    subgraph "Vector Databases"
        E[unified_chroma/chroma.sqlite3<br/>69.64 MB, 8 tables]
    end
    
    subgraph "External Data"
        F[ITIS.sqlite<br/>879.00 MB, 1 table]
    end
    
    subgraph "Content Management"
        G[content_db.sqlite<br/>0.00 MB, 0 tables]
        H[erdb.db<br/>0.00 MB, 0 tables]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
```

## Core Entity-Relationship Diagram

```mermaid
erDiagram
    %% User Management
    USERS {
        int user_id PK
        string username UK
        string email UK
        string password_hash
        string role
        int group_id FK
        string account_status
        timestamp created_at
        timestamp last_login
        int failed_login_attempts
        string profile_picture
        string full_name
        boolean email_verified
    }
    
    PERMISSION_GROUPS {
        int group_id PK
        string name UK
        string description
        string permissions
    }
    
    USER_SESSIONS {
        int session_id PK
        int user_id FK
        string session_token UK
        string device_fingerprint
        string ip_address
        string user_agent
        timestamp start_time
        timestamp end_time
        boolean is_active
    }
    
    %% Content Management
    PDF_DOCUMENTS {
        int id PK
        string filename
        string original_filename
        string category
        int source_url_id FK
        timestamp upload_date
        int file_size
        int page_count
        string pdf_title
        string pdf_author
        string pdf_subject
        string pdf_keywords
        timestamp pdf_creation_date
        timestamp pdf_modification_date
        timestamp created_at
        timestamp updated_at
    }
    
    SOURCE_URLS {
        int id PK
        string url UK
        string title
        string description
        timestamp last_scraped
        timestamp last_updated
        string status
        string error_message
        timestamp created_at
    }
    
    URL_CONTENT {
        int id PK
        int source_url_id FK
        string content_type
        string content
        int content_order
        string metadata
        timestamp created_at
    }
    
    COVER_IMAGES {
        int id PK
        int pdf_document_id FK
        string image_path
        string image_url
        string source
        string description
        timestamp created_at
    }
    
    %% Chat System
    CHAT_HISTORY {
        int id PK
        int user_id FK
        string category
        string question
        string answer
        string sources
        string images
        string pdf_links
        string metadata
        string url_images
        string pdf_images
        string document_thumbnails
        string client_name
        string session_id
        timestamp session_start
        timestamp session_end
        string device_fingerprint
        string anti_hallucination_mode
        string model_name
        string embedding_model
        string vision_model
        timestamp timestamp
    }
    
    CHAT_ANALYTICS {
        int id PK
        int chat_id FK
        string session_id
        string category
        string client_name
        int question_length
        int answer_length
        real processing_time
        int source_count
        int image_count
        int token_count
        string model_name
        string embedding_model
        string vision_model
        boolean vision_enabled
        int images_filtered
        int total_images_extracted
        string filter_sensitivity
        boolean hallucination_detected
        string device_fingerprint
        string ip_address
        string city
        string region
        string country
        real latitude
        real longitude
        timestamp timestamp
    }
    
    %% Location System
    EXTRACTED_LOCATIONS {
        int id PK
        string location_text
        string location_type
        real latitude
        real longitude
        real confidence_score
        string context_snippet
        string geocoded_address
        string country
        string region
        string city
        string municipality
        string barangay
        string administrative_level
        timestamp created_at
        timestamp updated_at
    }
    
    LOCATION_SOURCES {
        int id PK
        int location_id FK
        string source_type
        int source_id
        int page_number
        string extraction_method
        timestamp created_at
    }
    
    GEOCODING_CACHE {
        int id PK
        string location_query UK
        real latitude
        real longitude
        string formatted_address
        string geocoding_service
        real confidence_score
        timestamp cached_at
        timestamp expires_at
        string status
        string country
        string region
        string city
    }
    
    %% Greeting System
    GREETING_TEMPLATES {
        int id PK
        string template_type
        string greeting_text
        string context_conditions
        boolean is_active
        int weight
        timestamp created_at
        timestamp updated_at
    }
    
    USER_GREETING_PREFERENCES {
        int user_id PK
        string preferred_greeting_style
        int last_greeting_used
        string greeting_frequency
    }
    
    GREETING_ANALYTICS {
        int id PK
        string session_id
        string client_name
        int greeting_template_id FK
        string greeting_type
        timestamp timestamp
        real user_response_time
    }
    
    %% System Configuration
    SYSTEM_CONFIG {
        int id PK
        string config_key UK
        string config_value
        string description
        timestamp updated_at
    }
    
    DATABASE_VERSION {
        int id PK
        int version
        timestamp applied_at
        string description
    }
    
    MAINTENANCE_LOG {
        int id PK
        string operation
        string details
        real duration_seconds
        boolean success
        timestamp timestamp
    }
    
    %% Relationships
    USERS ||--o{ USER_SESSIONS : "has"
    USERS ||--o{ CHAT_HISTORY : "creates"
    USERS ||--o{ CHAT_ANALYTICS : "generates"
    USERS ||--o{ USER_GREETING_PREFERENCES : "has"
    
    PERMISSION_GROUPS ||--o{ USERS : "contains"
    
    SOURCE_URLS ||--o{ PDF_DOCUMENTS : "sources"
    SOURCE_URLS ||--o{ URL_CONTENT : "contains"
    
    PDF_DOCUMENTS ||--o{ COVER_IMAGES : "has"
    PDF_DOCUMENTS ||--o{ LOCATION_SOURCES : "contains"
    
    CHAT_HISTORY ||--o{ CHAT_ANALYTICS : "generates"
    
    EXTRACTED_LOCATIONS ||--o{ LOCATION_SOURCES : "referenced_by"
    
    GREETING_TEMPLATES ||--o{ GREETING_ANALYTICS : "used_in"
    GREETING_TEMPLATES ||--o{ USER_GREETING_PREFERENCES : "referenced_by"
```

## Database Schema Details

### 1. Main Application Database (`erdb_main.db`)

**Purpose**: Core application data storage
**Size**: 0.88 MB
**Tables**: 29

#### Key Tables:

1. **chat_history** - Chat interactions and responses
2. **chat_analytics** - Performance and usage analytics
3. **content_sources** - URL and content management
4. **pdf_documents** - Document metadata and storage
5. **source_urls** - External content sources
6. **url_content** - Extracted content from URLs
7. **cover_images** - Document cover images
8. **extracted_locations** - Geographic location data
9. **location_sources** - Location extraction sources
10. **geocoding_cache** - Cached geocoding results
11. **greeting_templates** - Dynamic greeting system
12. **user_greeting_preferences** - User greeting preferences
13. **greeting_analytics** - Greeting usage analytics
14. **system_config** - System configuration
15. **database_version** - Schema version tracking
16. **maintenance_log** - System maintenance records

### 2. Chat History Database (`chat_history.db`)

**Purpose**: Chat interactions and analytics
**Size**: 2.82 MB
**Tables**: 5

#### Key Tables:

1. **chat_history** - Main chat interactions (325 rows)
2. **chat_analytics** - Performance analytics (323 rows)
3. **geoip_analytics** - Geographic analytics (573 rows)
4. **session_metadata** - Session tracking (1 row)

### 3. User Management Database (`user_management.db`)

**Purpose**: User accounts and permissions
**Size**: 0.20 MB
**Tables**: 8

#### Key Tables:

1. **users** - User accounts and profiles
2. **permission_groups** - Role-based permissions
3. **user_sessions** - Active user sessions
4. **user_activity_logs** - User activity tracking
5. **permission_overrides** - Custom permissions
6. **dashboard_permissions** - Dashboard access control

### 4. Vector Database (`unified_chroma/chroma.sqlite3`)

**Purpose**: Vector embeddings for semantic search
**Size**: 69.64 MB
**Tables**: 8

#### Key Tables:

1. **embeddings** - Vector embeddings
2. **documents** - Document metadata
3. **collections** - Collection management
4. **metadata** - Embedding metadata

### 5. External Data (`ITIS.sqlite`)

**Purpose**: Taxonomic information system
**Size**: 879.00 MB
**Tables**: 1

#### Key Tables:

1. **ITIS** - Taxonomic data (large dataset)

## Data Flow Architecture

```mermaid
flowchart TD
    subgraph "User Interface"
        UI[Web Interface]
        API[API Endpoints]
    end
    
    subgraph "Application Layer"
        AUTH[Authentication]
        CHAT[Chat Service]
        DOC[Document Service]
        VECTOR[Vector Service]
    end
    
    subgraph "Data Layer"
        MAIN[erdb_main.db]
        CHAT_DB[chat_history.db]
        USER_DB[user_management.db]
        VECTOR_DB[unified_chroma]
        ITIS[ITIS.sqlite]
    end
    
    UI --> API
    API --> AUTH
    API --> CHAT
    API --> DOC
    API --> VECTOR
    
    AUTH --> USER_DB
    CHAT --> CHAT_DB
    CHAT --> MAIN
    DOC --> MAIN
    DOC --> VECTOR_DB
    VECTOR --> VECTOR_DB
    VECTOR --> ITIS
```

## Key Relationships and Constraints

### 1. User Management Relationships

```sql
-- Users belong to permission groups
ALTER TABLE users ADD CONSTRAINT fk_users_group 
FOREIGN KEY (group_id) REFERENCES permission_groups(group_id);

-- User sessions are linked to users
ALTER TABLE user_sessions ADD CONSTRAINT fk_sessions_user 
FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;
```

### 2. Content Management Relationships

```sql
-- PDF documents can be sourced from URLs
ALTER TABLE pdf_documents ADD CONSTRAINT fk_pdf_source 
FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE SET NULL;

-- URL content belongs to source URLs
ALTER TABLE url_content ADD CONSTRAINT fk_content_source 
FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE CASCADE;

-- Cover images belong to PDF documents
ALTER TABLE cover_images ADD CONSTRAINT fk_cover_pdf 
FOREIGN KEY (pdf_document_id) REFERENCES pdf_documents(id) ON DELETE CASCADE;
```

### 3. Chat System Relationships

```sql
-- Chat analytics are linked to chat history
ALTER TABLE chat_analytics ADD CONSTRAINT fk_analytics_chat 
FOREIGN KEY (chat_id) REFERENCES chat_history(id) ON DELETE CASCADE;

-- Chat history can be linked to users
ALTER TABLE chat_history ADD CONSTRAINT fk_chat_user 
FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL;
```

### 4. Location System Relationships

```sql
-- Location sources reference extracted locations
ALTER TABLE location_sources ADD CONSTRAINT fk_location_source 
FOREIGN KEY (location_id) REFERENCES extracted_locations(id) ON DELETE CASCADE;
```

## Performance Considerations

### 1. Indexing Strategy

```sql
-- Primary indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_chat_history_category ON chat_history(category);
CREATE INDEX idx_chat_history_timestamp ON chat_history(timestamp);
CREATE INDEX idx_pdf_documents_category ON pdf_documents(category);
CREATE INDEX idx_pdf_documents_filename ON pdf_documents(filename);
CREATE INDEX idx_extracted_locations_coordinates ON extracted_locations(latitude, longitude);
```

### 2. Database Optimization

```sql
-- SQLite optimizations
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = *********;
PRAGMA foreign_keys = ON;
PRAGMA busy_timeout = 30000;
```

## Data Volume Analysis

| Database | Size (MB) | Tables | Primary Purpose | Data Volume |
|----------|-----------|--------|-----------------|-------------|
| erdb_main.db | 0.88 | 29 | Core application | Medium |
| chat_history.db | 2.82 | 5 | Chat interactions | High |
| user_management.db | 0.20 | 8 | User accounts | Low |
| unified_chroma | 69.64 | 8 | Vector embeddings | High |
| ITIS.sqlite | 879.00 | 1 | Taxonomic data | Very High |
| scraped_pages.db | 0.01 | 1 | Web scraping | Low |

## Recommendations

### 1. Database Consolidation

- **Immediate**: Consolidate `chat_history.db` into `erdb_main.db`
- **Short-term**: Merge `user_management.db` into main database
- **Long-term**: Consider PostgreSQL migration for production

### 2. Performance Optimization

- Implement connection pooling
- Add missing indexes for frequently queried columns
- Optimize vector database queries
- Implement data archiving for old chat history

### 3. Data Management

- Implement automated backup strategy
- Add data retention policies
- Monitor database growth
- Implement data compression for large tables

### 4. Security Enhancements

- Encrypt sensitive data at rest
- Implement row-level security
- Add audit logging for all data changes
- Regular security assessments

## Conclusion

The ERDB system uses a well-structured multi-database architecture that effectively separates concerns while maintaining data integrity. The system handles document management, chat interactions, user management, and vector search capabilities efficiently. The main areas for improvement are database consolidation and performance optimization for production deployment. 