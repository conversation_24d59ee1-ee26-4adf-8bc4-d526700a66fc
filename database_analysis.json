{"analysis_timestamp": "2025-08-07T07:21:22.163252", "databases": {".\\chat_history.db": {"path": ".\\chat_history.db", "size_bytes": 2953216, "size_mb": 2.82, "page_count": 721, "page_size": 4096, "tables": {"chat_history": {"row_count": 325, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "category", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "question", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "answer", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "sources", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "DATETIME", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "images", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_links", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "url_images", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_images", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "document_thumbnails", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "session_start", "type": "DATETIME", "not_null": false, "default": null, "primary_key": false}, {"name": "session_end", "type": "DATETIME", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "model_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "sqlite_sequence": {"row_count": 4, "columns": [{"name": "name", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "chat_analytics": {"row_count": 323, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "chat_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "question_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "answer_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "processing_time", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "source_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "image_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "token_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "DATETIME", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "model_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_enabled", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "images_filtered", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "total_images_extracted", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "filter_sensitivity", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "hallucination_detected", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "geoip_analytics": {"row_count": 573, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "DATETIME", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "page_url", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "session_metadata": {"row_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "first_visit_timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "session_count", "type": "INTEGER", "not_null": false, "default": "1", "primary_key": false}, {"name": "timezone", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "local_time", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "time_of_day", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_session_metadata_1", "unique": true, "columns": ["session_id"]}]}}}, ".\\erdb_main.db": {"path": ".\\erdb_main.db", "size_bytes": 925696, "size_mb": 0.88, "page_count": 226, "page_size": 4096, "tables": {"chat_history": {"row_count": 123, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "category", "type": "VARCHAR(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "question", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "answer", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "VARCHAR(20)", "not_null": false, "default": "'strict'", "primary_key": false}, {"name": "model_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_chat_history_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_chat_history_category", "unique": false, "columns": ["category"]}]}, "sqlite_sequence": {"row_count": 19, "columns": [{"name": "name", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "chat_analytics": {"row_count": 123, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "chat_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "question_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "answer_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "processing_time", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "source_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "image_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "token_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "model_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_enabled", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "images_filtered", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "total_images_extracted", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "filter_sensitivity", "type": "VARCHAR(20)", "not_null": false, "default": null, "primary_key": false}, {"name": "hallucination_detected", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "VARCHAR(20)", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "VARCHAR(45)", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_chat_analytics_chat_id", "unique": false, "columns": ["chat_id"]}]}, "content_sources": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "content", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "source_type", "type": "VARCHAR(50)", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_content_sources_url", "unique": false, "columns": ["url"]}]}, "scraped_pages": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "filename", "type": "VARCHAR(255)", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_scraped", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": []}, "sqlite_stat1": {"row_count": 49, "columns": [{"name": "tbl", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "idx", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "stat", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "source_urls": {"row_count": 7, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "last_scraped", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_source_urls_1", "unique": true, "columns": ["url"]}]}, "url_content": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "source_url_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "content_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "content", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "content_order", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_url_content_content_type", "unique": false, "columns": ["content_type"]}, {"name": "idx_url_content_source_url_id", "unique": false, "columns": ["source_url_id"]}]}, "cover_images": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "pdf_document_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "image_path", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "image_url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "source", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_cover_images_pdf_document_id", "unique": true, "columns": ["pdf_document_id"]}]}, "database_version": {"row_count": 5, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "version", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "applied_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "greeting_templates": {"row_count": 21, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "template_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "greeting_text", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "context_conditions", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}, {"name": "weight", "type": "INTEGER", "not_null": false, "default": "1", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_greeting_templates_active", "unique": false, "columns": ["is_active"]}, {"name": "idx_greeting_templates_type", "unique": false, "columns": ["template_type"]}]}, "user_greeting_preferences": {"row_count": 0, "columns": [{"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "preferred_greeting_style", "type": "TEXT", "not_null": false, "default": "'friendly'", "primary_key": false}, {"name": "last_greeting_used", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_frequency", "type": "TEXT", "not_null": false, "default": "'every_response'", "primary_key": false}], "indexes": []}, "greeting_analytics": {"row_count": 415, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_template_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "user_response_time", "type": "REAL", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_greeting_analytics_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_greeting_analytics_session", "unique": false, "columns": ["session_id"]}]}, "extracted_locations": {"row_count": 327, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_text", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "location_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "default": "0.0", "primary_key": false}, {"name": "context_snippet", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "geocoded_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "municipality", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "barangay", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "administrative_level", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_extracted_locations_coordinates", "unique": false, "columns": ["latitude", "longitude"]}, {"name": "idx_extracted_locations_type", "unique": false, "columns": ["location_type"]}]}, "location_sources": {"row_count": 327, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "source_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "source_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "page_number", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "extraction_method", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_location_sources_location_id", "unique": false, "columns": ["location_id"]}, {"name": "idx_location_sources_type", "unique": false, "columns": ["source_type"]}]}, "geocoding_cache": {"row_count": 97, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_query", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "formatted_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "geocoding_service", "type": "TEXT", "not_null": false, "default": "'nominatim'", "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "default": "0.0", "primary_key": false}, {"name": "cached_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "expires_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": "'success'", "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_geocoding_cache_expires", "unique": false, "columns": ["expires_at"]}, {"name": "idx_geocoding_cache_query", "unique": false, "columns": ["location_query"]}, {"name": "sqlite_autoindex_geocoding_cache_1", "unique": true, "columns": ["location_query"]}]}, "users": {"row_count": 3, "columns": [{"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "username", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "password_hash", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "role", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_login", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "account_status", "type": "TEXT", "not_null": false, "default": "'pending'", "primary_key": false}, {"name": "failed_login_attempts", "type": "INTEGER", "not_null": false, "default": "0", "primary_key": false}, {"name": "reset_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "reset_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "email_verified", "type": "BOOLEAN", "not_null": false, "default": "0", "primary_key": false}, {"name": "verification_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "verification_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "password_changed_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "profile_picture", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "full_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "notification_preferences", "type": "TEXT", "not_null": false, "default": "'{\"email_alerts\": true, \"security_notifications\": true}'", "primary_key": false}], "indexes": [{"name": "idx_users_group_id", "unique": false, "columns": ["group_id"]}, {"name": "idx_users_account_status", "unique": false, "columns": ["account_status"]}, {"name": "idx_users_role", "unique": false, "columns": ["role"]}, {"name": "idx_users_email", "unique": false, "columns": ["email"]}, {"name": "idx_users_username", "unique": false, "columns": ["username"]}, {"name": "sqlite_autoindex_users_2", "unique": true, "columns": ["email"]}, {"name": "sqlite_autoindex_users_1", "unique": true, "columns": ["username"]}]}, "permission_groups": {"row_count": 4, "columns": [{"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_groups_name", "unique": false, "columns": ["name"]}, {"name": "sqlite_autoindex_permission_groups_1", "unique": true, "columns": ["name"]}]}, "group_permissions": {"row_count": 60, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "group_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_group_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_group_permissions_group_id", "unique": false, "columns": ["group_id"]}, {"name": "sqlite_autoindex_group_permissions_1", "unique": true, "columns": ["group_id", "function_name"]}]}, "permission_overrides": {"row_count": 17, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_overrides_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_permission_overrides_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_permission_overrides_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "permission_audit_logs": {"row_count": 27, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "admin_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "target_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "change_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "entity_changed", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "old_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "new_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_permission_audit_logs_change_type", "unique": false, "columns": ["change_type"]}, {"name": "idx_permission_audit_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_permission_audit_logs_target_user_id", "unique": false, "columns": ["target_user_id"]}, {"name": "idx_permission_audit_logs_admin_user_id", "unique": false, "columns": ["admin_user_id"]}]}, "dashboard_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_dashboard_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_dashboard_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_dashboard_permissions_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "category_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "permission", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_category_permissions_category", "unique": false, "columns": ["category"]}, {"name": "idx_category_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_category_permissions_1", "unique": true, "columns": ["user_id", "category"]}]}, "user_activity_logs": {"row_count": 11, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "action_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "details", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_user_activity_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_user_activity_logs_action_type", "unique": false, "columns": ["action_type"]}, {"name": "idx_user_activity_logs_user_id", "unique": false, "columns": ["user_id"]}]}, "user_sessions": {"row_count": 0, "columns": [{"name": "session_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "session_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "start_time", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "end_time", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}], "indexes": [{"name": "idx_user_sessions_device_fingerprint", "unique": false, "columns": ["device_fingerprint"]}, {"name": "idx_user_sessions_session_token", "unique": false, "columns": ["session_token"]}, {"name": "idx_user_sessions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_user_sessions_1", "unique": true, "columns": ["session_token"]}]}, "forms": {"row_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "fields", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_forms_active", "unique": false, "columns": ["is_active"]}]}, "form_submissions": {"row_count": 21, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "form_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_document_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "submission_data", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "submitted_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_form_submissions_submitted_at", "unique": false, "columns": ["submitted_at"]}, {"name": "idx_form_submissions_pdf_document_id", "unique": false, "columns": ["pdf_document_id"]}, {"name": "idx_form_submissions_form_id", "unique": false, "columns": ["form_id"]}]}, "categories": {"row_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "form_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_categories_form_id", "unique": false, "columns": ["form_id"]}, {"name": "sqlite_autoindex_categories_1", "unique": true, "columns": ["name"]}]}, "pdf_documents": {"row_count": 63, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "original_filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "upload_date", "type": "TIMESTAMP", "not_null": true, "default": null, "primary_key": false}, {"name": "source_url_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "form_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "file_size", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "page_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "download_filename", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "has_non_ocr_version", "type": "BOOLEAN", "not_null": false, "default": "FALSE", "primary_key": false}, {"name": "conversion_settings", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "published_year", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_start", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_end", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_range_str", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_author", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_subject", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_keywords", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_creation_date", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_modification_date", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_version", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_producer", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_creator", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_pdf_documents_subject", "unique": false, "columns": ["pdf_subject"]}, {"name": "idx_pdf_documents_author", "unique": false, "columns": ["pdf_author"]}, {"name": "idx_pdf_documents_title", "unique": false, "columns": ["pdf_title"]}, {"name": "idx_pdf_documents_form_id", "unique": false, "columns": ["form_id"]}, {"name": "idx_pdf_documents_category", "unique": false, "columns": ["category"]}, {"name": "idx_pdf_documents_filename", "unique": false, "columns": ["filename"]}]}}}, ".\\erdb_main_corrupted_20250805_155401.db": {"error": "database disk image is malformed"}, ".\\erdb_main_corrupted_20250805_155813.db": {"path": ".\\erdb_main_corrupted_20250805_155813.db", "size_bytes": 806912, "size_mb": 0.77, "page_count": 197, "page_size": 4096, "tables": {"chat_history": {"row_count": 123, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "category", "type": "VARCHAR(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "question", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "answer", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "VARCHAR(20)", "not_null": false, "default": "'strict'", "primary_key": false}, {"name": "model_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_chat_history_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_chat_history_category", "unique": false, "columns": ["category"]}]}, "sqlite_sequence": {"row_count": 19, "columns": [{"name": "name", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "chat_analytics": {"row_count": 123, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "chat_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "session_id", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "question_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "answer_length", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "processing_time", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "source_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "image_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "token_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "model_name", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "embedding_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_model", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "vision_enabled", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "images_filtered", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "total_images_extracted", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "filter_sensitivity", "type": "VARCHAR(20)", "not_null": false, "default": null, "primary_key": false}, {"name": "hallucination_detected", "type": "BOOLEAN", "not_null": false, "default": null, "primary_key": false}, {"name": "anti_hallucination_mode", "type": "VARCHAR(20)", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "VARCHAR(255)", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "VARCHAR(45)", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_chat_analytics_chat_id", "unique": false, "columns": ["chat_id"]}]}, "content_sources": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "content", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": false, "default": null, "primary_key": false}, {"name": "source_type", "type": "VARCHAR(50)", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_content_sources_url", "unique": false, "columns": ["url"]}]}, "scraped_pages": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "filename", "type": "VARCHAR(255)", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "VARCHAR(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_scraped", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": []}, "sqlite_stat1": {"row_count": 3, "columns": [{"name": "tbl", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "idx", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "stat", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "source_urls": {"row_count": 7, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "last_scraped", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "last_updated", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "error_message", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_source_urls_1", "unique": true, "columns": ["url"]}]}, "url_content": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "source_url_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "content_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "content", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "content_order", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_url_content_content_type", "unique": false, "columns": ["content_type"]}, {"name": "idx_url_content_source_url_id", "unique": false, "columns": ["source_url_id"]}]}, "cover_images": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "pdf_document_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "image_path", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "image_url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "source", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_cover_images_pdf_document_id", "unique": true, "columns": ["pdf_document_id"]}]}, "database_version": {"row_count": 5, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "version", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "applied_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "greeting_templates": {"row_count": 21, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "template_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "greeting_text", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "context_conditions", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}, {"name": "weight", "type": "INTEGER", "not_null": false, "default": "1", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_greeting_templates_active", "unique": false, "columns": ["is_active"]}, {"name": "idx_greeting_templates_type", "unique": false, "columns": ["template_type"]}]}, "user_greeting_preferences": {"row_count": 0, "columns": [{"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "preferred_greeting_style", "type": "TEXT", "not_null": false, "default": "'friendly'", "primary_key": false}, {"name": "last_greeting_used", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_frequency", "type": "TEXT", "not_null": false, "default": "'every_response'", "primary_key": false}], "indexes": []}, "greeting_analytics": {"row_count": 262, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "session_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "client_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_template_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "greeting_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "user_response_time", "type": "REAL", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_greeting_analytics_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_greeting_analytics_session", "unique": false, "columns": ["session_id"]}]}, "extracted_locations": {"row_count": 208, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_text", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "location_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "default": "0.0", "primary_key": false}, {"name": "context_snippet", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "geocoded_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "municipality", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "barangay", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "administrative_level", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_extracted_locations_coordinates", "unique": false, "columns": ["latitude", "longitude"]}, {"name": "idx_extracted_locations_type", "unique": false, "columns": ["location_type"]}]}, "location_sources": {"row_count": 208, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "source_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "source_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "page_number", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "extraction_method", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_location_sources_location_id", "unique": false, "columns": ["location_id"]}, {"name": "idx_location_sources_type", "unique": false, "columns": ["source_type"]}]}, "geocoding_cache": {"row_count": 44, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "location_query", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "latitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "longitude", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "formatted_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "geocoding_service", "type": "TEXT", "not_null": false, "default": "'nominatim'", "primary_key": false}, {"name": "confidence_score", "type": "REAL", "not_null": false, "default": "0.0", "primary_key": false}, {"name": "cached_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "expires_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": "'success'", "primary_key": false}, {"name": "country", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "region", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "city", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_geocoding_cache_expires", "unique": false, "columns": ["expires_at"]}, {"name": "idx_geocoding_cache_query", "unique": false, "columns": ["location_query"]}, {"name": "sqlite_autoindex_geocoding_cache_1", "unique": true, "columns": ["location_query"]}]}, "users": {"row_count": 3, "columns": [{"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "username", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "password_hash", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "role", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_login", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "account_status", "type": "TEXT", "not_null": false, "default": "'pending'", "primary_key": false}, {"name": "failed_login_attempts", "type": "INTEGER", "not_null": false, "default": "0", "primary_key": false}, {"name": "reset_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "reset_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "email_verified", "type": "BOOLEAN", "not_null": false, "default": "0", "primary_key": false}, {"name": "verification_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "verification_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "password_changed_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "profile_picture", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "full_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "notification_preferences", "type": "TEXT", "not_null": false, "default": "'{\"email_alerts\": true, \"security_notifications\": true}'", "primary_key": false}], "indexes": [{"name": "idx_users_group_id", "unique": false, "columns": ["group_id"]}, {"name": "idx_users_account_status", "unique": false, "columns": ["account_status"]}, {"name": "idx_users_role", "unique": false, "columns": ["role"]}, {"name": "idx_users_email", "unique": false, "columns": ["email"]}, {"name": "idx_users_username", "unique": false, "columns": ["username"]}, {"name": "sqlite_autoindex_users_2", "unique": true, "columns": ["email"]}, {"name": "sqlite_autoindex_users_1", "unique": true, "columns": ["username"]}]}, "permission_groups": {"row_count": 4, "columns": [{"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_groups_name", "unique": false, "columns": ["name"]}, {"name": "sqlite_autoindex_permission_groups_1", "unique": true, "columns": ["name"]}]}, "group_permissions": {"row_count": 60, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "group_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_group_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_group_permissions_group_id", "unique": false, "columns": ["group_id"]}, {"name": "sqlite_autoindex_group_permissions_1", "unique": true, "columns": ["group_id", "function_name"]}]}, "permission_overrides": {"row_count": 17, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_overrides_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_permission_overrides_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_permission_overrides_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "permission_audit_logs": {"row_count": 27, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "admin_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "target_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "change_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "entity_changed", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "old_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "new_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_permission_audit_logs_change_type", "unique": false, "columns": ["change_type"]}, {"name": "idx_permission_audit_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_permission_audit_logs_target_user_id", "unique": false, "columns": ["target_user_id"]}, {"name": "idx_permission_audit_logs_admin_user_id", "unique": false, "columns": ["admin_user_id"]}]}, "dashboard_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_dashboard_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_dashboard_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_dashboard_permissions_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "category_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "permission", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_category_permissions_category", "unique": false, "columns": ["category"]}, {"name": "idx_category_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_category_permissions_1", "unique": true, "columns": ["user_id", "category"]}]}, "user_activity_logs": {"row_count": 11, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "action_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "details", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_user_activity_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_user_activity_logs_action_type", "unique": false, "columns": ["action_type"]}, {"name": "idx_user_activity_logs_user_id", "unique": false, "columns": ["user_id"]}]}, "user_sessions": {"row_count": 0, "columns": [{"name": "session_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "session_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "start_time", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "end_time", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}], "indexes": [{"name": "idx_user_sessions_device_fingerprint", "unique": false, "columns": ["device_fingerprint"]}, {"name": "idx_user_sessions_session_token", "unique": false, "columns": ["session_token"]}, {"name": "idx_user_sessions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_user_sessions_1", "unique": true, "columns": ["session_token"]}]}, "forms": {"row_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "fields", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_forms_active", "unique": false, "columns": ["is_active"]}]}, "form_submissions": {"row_count": 19, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "form_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_document_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "submission_data", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "submitted_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_form_submissions_submitted_at", "unique": false, "columns": ["submitted_at"]}, {"name": "idx_form_submissions_pdf_document_id", "unique": false, "columns": ["pdf_document_id"]}, {"name": "idx_form_submissions_form_id", "unique": false, "columns": ["form_id"]}]}, "categories": {"row_count": 3, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "form_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_categories_form_id", "unique": false, "columns": ["form_id"]}, {"name": "sqlite_autoindex_categories_1", "unique": true, "columns": ["name"]}]}, "pdf_documents": {"row_count": 78, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "original_filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "upload_date", "type": "TIMESTAMP", "not_null": true, "default": null, "primary_key": false}, {"name": "source_url_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "form_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "file_size", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "page_count", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "download_filename", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "has_non_ocr_version", "type": "BOOLEAN", "not_null": false, "default": "FALSE", "primary_key": false}, {"name": "conversion_settings", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "published_year", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_start", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_end", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "published_month_range_str", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_title", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_author", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_subject", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_keywords", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_creation_date", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_modification_date", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_version", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_producer", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "pdf_creator", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_pdf_documents_subject", "unique": false, "columns": ["pdf_subject"]}, {"name": "idx_pdf_documents_author", "unique": false, "columns": ["pdf_author"]}, {"name": "idx_pdf_documents_title", "unique": false, "columns": ["pdf_title"]}, {"name": "idx_pdf_documents_form_id", "unique": false, "columns": ["form_id"]}, {"name": "idx_pdf_documents_category", "unique": false, "columns": ["category"]}, {"name": "idx_pdf_documents_filename", "unique": false, "columns": ["filename"]}]}}}, ".\\scraped_pages.db": {"path": ".\\scraped_pages.db", "size_bytes": 12288, "size_mb": 0.01, "page_count": 3, "page_size": 4096, "tables": {"scraped_pages": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "url", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "timestamp", "type": "DATETIME", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": []}, "sqlite_sequence": {"row_count": 0, "columns": [{"name": "name", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}}}, ".\\user_management.db": {"path": ".\\user_management.db", "size_bytes": 204800, "size_mb": 0.2, "page_count": 50, "page_size": 4096, "tables": {"users": {"row_count": 3, "columns": [{"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "username", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "password_hash", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "role", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "last_login", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "account_status", "type": "TEXT", "not_null": false, "default": "'pending'", "primary_key": false}, {"name": "failed_login_attempts", "type": "INTEGER", "not_null": false, "default": "0", "primary_key": false}, {"name": "reset_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "reset_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "email_verified", "type": "BOOLEAN", "not_null": false, "default": "0", "primary_key": false}, {"name": "verification_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "verification_token_expiry", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "password_changed_at", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "profile_picture", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "full_name", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "notification_preferences", "type": "TEXT", "not_null": false, "default": "'{\"email_alerts\": true, \"security_notifications\": true}'", "primary_key": false}, {"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_users_group_id", "unique": false, "columns": ["group_id"]}, {"name": "idx_users_account_status", "unique": false, "columns": ["account_status"]}, {"name": "idx_users_role", "unique": false, "columns": ["role"]}, {"name": "idx_users_email", "unique": false, "columns": ["email"]}, {"name": "idx_users_username", "unique": false, "columns": ["username"]}, {"name": "sqlite_autoindex_users_2", "unique": true, "columns": ["email"]}, {"name": "sqlite_autoindex_users_1", "unique": true, "columns": ["username"]}]}, "sqlite_sequence": {"row_count": 6, "columns": [{"name": "name", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "category_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "permission", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_category_permissions_category", "unique": false, "columns": ["category"]}, {"name": "idx_category_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_category_permissions_1", "unique": true, "columns": ["user_id", "category"]}]}, "user_activity_logs": {"row_count": 100, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "action_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "details", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_type", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "resource_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_user_activity_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_user_activity_logs_action_type", "unique": false, "columns": ["action_type"]}, {"name": "idx_user_activity_logs_user_id", "unique": false, "columns": ["user_id"]}]}, "dashboard_permissions": {"row_count": 0, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_dashboard_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_dashboard_permissions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_dashboard_permissions_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "permission_groups": {"row_count": 4, "columns": [{"name": "group_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_groups_name", "unique": false, "columns": ["name"]}, {"name": "sqlite_autoindex_permission_groups_1", "unique": true, "columns": ["name"]}]}, "group_permissions": {"row_count": 60, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "group_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_group_permissions_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_group_permissions_group_id", "unique": false, "columns": ["group_id"]}, {"name": "sqlite_autoindex_group_permissions_1", "unique": true, "columns": ["group_id", "function_name"]}]}, "permission_overrides": {"row_count": 10, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "function_name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "enabled", "type": "BOOLEAN", "not_null": true, "default": "0", "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "idx_permission_overrides_function", "unique": false, "columns": ["function_name"]}, {"name": "idx_permission_overrides_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_permission_overrides_1", "unique": true, "columns": ["user_id", "function_name"]}]}, "permission_audit_logs": {"row_count": 26, "columns": [{"name": "log_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "admin_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "target_user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "change_type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "entity_changed", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "old_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "new_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "idx_permission_audit_logs_change_type", "unique": false, "columns": ["change_type"]}, {"name": "idx_permission_audit_logs_timestamp", "unique": false, "columns": ["timestamp"]}, {"name": "idx_permission_audit_logs_target_user_id", "unique": false, "columns": ["target_user_id"]}, {"name": "idx_permission_audit_logs_admin_user_id", "unique": false, "columns": ["admin_user_id"]}]}, "user_sessions": {"row_count": 0, "columns": [{"name": "session_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "session_token", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "device_fingerprint", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "ip_address", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "user_agent", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "start_time", "type": "TIMESTAMP", "not_null": false, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "end_time", "type": "TIMESTAMP", "not_null": false, "default": null, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "default": "1", "primary_key": false}], "indexes": [{"name": "idx_user_sessions_device_fingerprint", "unique": false, "columns": ["device_fingerprint"]}, {"name": "idx_user_sessions_session_token", "unique": false, "columns": ["session_token"]}, {"name": "idx_user_sessions_user_id", "unique": false, "columns": ["user_id"]}, {"name": "sqlite_autoindex_user_sessions_1", "unique": true, "columns": ["session_token"]}]}, "sqlite_stat1": {"row_count": 22, "columns": [{"name": "tbl", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "idx", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "stat", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}}}, ".\\data\\content_db.sqlite": {"path": ".\\data\\content_db.sqlite", "size_bytes": 0, "size_mb": 0.0, "page_count": 0, "page_size": 4096, "tables": {}}, ".\\data\\erdb.db": {"path": ".\\data\\erdb.db", "size_bytes": 0, "size_mb": 0.0, "page_count": 0, "page_size": 4096, "tables": {}}, ".\\data\\itisSqlite060625\\ITIS.sqlite": {"path": ".\\data\\itisSqlite060625\\ITIS.sqlite", "size_bytes": 922046464, "size_mb": 879.33, "page_count": 900436, "page_size": 1024, "tables": {"HierarchyToRank": {"row_count": 0, "columns": [{"name": "kingdom_tsn", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "level", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "rank_id", "type": "smallint(6)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "rank_name", "type": "char(15)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "tsn", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "parent_tsn", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "scientific_name", "type": "<PERSON><PERSON><PERSON>(163)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "taxon_author", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "credibility_rtng", "type": "<PERSON><PERSON><PERSON>(40)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "sort", "type": "<PERSON><PERSON><PERSON>(400)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "DirectChildrenCount", "type": "int(11)", "not_null": true, "default": null, "primary_key": false}, {"name": "synonyms", "type": "<PERSON><PERSON><PERSON>(1000)", "not_null": false, "default": "NULL", "primary_key": false}], "indexes": []}, "change_comments": {"row_count": 0, "columns": [{"name": "change_track_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "chg_cmt_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "change_detail", "type": "<PERSON><PERSON><PERSON>(250)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "change_comments_change_comments_index", "unique": false, "columns": ["change_track_id", "chg_cmt_id"]}, {"name": "sqlite_autoindex_change_comments_1", "unique": true, "columns": ["change_track_id", "chg_cmt_id"]}]}, "change_operations": {"row_count": 0, "columns": [{"name": "change_track_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "chg_op_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "change_operations_change_operations_index", "unique": false, "columns": ["change_track_id", "chg_op_id"]}, {"name": "sqlite_autoindex_change_operations_1", "unique": true, "columns": ["change_track_id", "chg_op_id"]}]}, "change_tracks": {"row_count": 0, "columns": [{"name": "change_track_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "old_tsn", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "change_reason", "type": "<PERSON><PERSON><PERSON>(40)", "not_null": true, "default": null, "primary_key": false}, {"name": "change_initiator", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "change_reviewer", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "change_certifier", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "change_time_stamp", "type": "datetime", "not_null": true, "default": null, "primary_key": false}, {"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "change_tracks_change_tracks_index", "unique": false, "columns": ["change_track_id"]}, {"name": "sqlite_autoindex_change_tracks_1", "unique": true, "columns": ["change_track_id"]}]}, "chg_operation_lkp": {"row_count": 0, "columns": [{"name": "chg_op_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "change_operation", "type": "<PERSON><PERSON><PERSON>(25)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "chg_operation_lkp_chg_operation_lkp_index", "unique": false, "columns": ["chg_op_id"]}, {"name": "sqlite_autoindex_chg_operation_lkp_1", "unique": true, "columns": ["chg_op_id"]}]}, "comments": {"row_count": 70054, "columns": [{"name": "comment_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "commentator", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "comment_detail", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "comment_time_stamp", "type": "datetime", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "comments_comments_index", "unique": false, "columns": ["comment_id"]}, {"name": "sqlite_autoindex_comments_1", "unique": true, "columns": ["comment_id"]}]}, "experts": {"row_count": 197, "columns": [{"name": "expert_id_prefix", "type": "char(3)", "not_null": true, "default": null, "primary_key": true}, {"name": "expert_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "expert", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "exp_comment", "type": "var<PERSON><PERSON>(500)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "experts_experts_index", "unique": false, "columns": ["expert_id_prefix", "expert_id"]}, {"name": "sqlite_autoindex_experts_1", "unique": true, "columns": ["expert_id_prefix", "expert_id"]}]}, "geographic_div": {"row_count": 459772, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "geographic_value", "type": "<PERSON><PERSON><PERSON>(45)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "geographic_div_geographic_index", "unique": false, "columns": ["tsn", "geographic_value"]}, {"name": "sqlite_autoindex_geographic_div_1", "unique": true, "columns": ["tsn", "geographic_value"]}]}, "hierarchy": {"row_count": 668613, "columns": [{"name": "hierarchy_string", "type": "<PERSON><PERSON><PERSON>(300)", "not_null": true, "default": null, "primary_key": true}, {"name": "TSN", "type": "int(11)", "not_null": true, "default": null, "primary_key": false}, {"name": "Parent_TSN", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "level", "type": "int(11)", "not_null": true, "default": null, "primary_key": false}, {"name": "ChildrenCount", "type": "int(11)", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "hierarchy_hierarchy_string", "unique": false, "columns": ["hierarchy_string"]}, {"name": "sqlite_autoindex_hierarchy_1", "unique": true, "columns": ["hierarchy_string"]}]}, "jurisdiction": {"row_count": 158777, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "jurisdiction_value", "type": "<PERSON><PERSON><PERSON>(30)", "not_null": true, "default": null, "primary_key": true}, {"name": "origin", "type": "<PERSON><PERSON><PERSON>(19)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "jurisdiction_jurisdiction_index", "unique": false, "columns": ["tsn", "jurisdiction_value"]}, {"name": "sqlite_autoindex_jurisdiction_1", "unique": true, "columns": ["tsn", "jurisdiction_value"]}]}, "kingdoms": {"row_count": 7, "columns": [{"name": "kingdom_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "kingdom_name", "type": "char(10)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "kingdoms_kingdoms_index", "unique": false, "columns": ["kingdom_id", "kingdom_name"]}, {"name": "sqlite_autoindex_kingdoms_1", "unique": true, "columns": ["kingdom_id"]}]}, "longnames": {"row_count": 970694, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "completename", "type": "<PERSON><PERSON><PERSON>(164)", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "longnames_tsn", "unique": false, "columns": ["tsn", "completename"]}, {"name": "sqlite_autoindex_longnames_1", "unique": true, "columns": ["tsn"]}]}, "nodc_ids": {"row_count": 209565, "columns": [{"name": "nodc_id", "type": "char(12)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}], "indexes": [{"name": "nodc_ids_nodc_index", "unique": false, "columns": ["nodc_id", "tsn"]}, {"name": "sqlite_autoindex_nodc_ids_1", "unique": true, "columns": ["nodc_id", "tsn"]}]}, "other_sources": {"row_count": 1035, "columns": [{"name": "source_id_prefix", "type": "char(3)", "not_null": true, "default": null, "primary_key": true}, {"name": "source_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "source_type", "type": "char(10)", "not_null": true, "default": null, "primary_key": false}, {"name": "source", "type": "<PERSON><PERSON><PERSON>(64)", "not_null": true, "default": null, "primary_key": false}, {"name": "version", "type": "char(10)", "not_null": true, "default": null, "primary_key": false}, {"name": "acquisition_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "source_comment", "type": "var<PERSON><PERSON>(500)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "other_sources_other_sources_index", "unique": false, "columns": ["source_id_prefix", "source_id"]}, {"name": "sqlite_autoindex_other_sources_1", "unique": true, "columns": ["source_id_prefix", "source_id"]}]}, "publications": {"row_count": 27454, "columns": [{"name": "pub_id_prefix", "type": "char(3)", "not_null": true, "default": null, "primary_key": true}, {"name": "publication_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "reference_author", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "publication_name", "type": "<PERSON><PERSON><PERSON>(255)", "not_null": true, "default": null, "primary_key": false}, {"name": "listed_pub_date", "type": "date", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "actual_pub_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "publisher", "type": "<PERSON><PERSON><PERSON>(80)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "pub_place", "type": "<PERSON><PERSON><PERSON>(40)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "isbn", "type": "<PERSON><PERSON><PERSON>(16)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "issn", "type": "<PERSON><PERSON><PERSON>(16)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "pages", "type": "<PERSON><PERSON><PERSON>(15)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "pub_comment", "type": "var<PERSON><PERSON>(500)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "publications_publications_index", "unique": false, "columns": ["pub_id_prefix", "publication_id"]}, {"name": "sqlite_autoindex_publications_1", "unique": true, "columns": ["pub_id_prefix", "publication_id"]}]}, "reference_links": {"row_count": 1900706, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "doc_id_prefix", "type": "char(3)", "not_null": true, "default": null, "primary_key": true}, {"name": "documentation_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "original_desc_ind", "type": "char(1)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "init_itis_desc_ind", "type": "char(1)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "change_track_id", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "vernacular_name", "type": "<PERSON><PERSON><PERSON>(80)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "reference_links_reference_links_index", "unique": false, "columns": ["tsn", "doc_id_prefix", "documentation_id"]}, {"name": "sqlite_autoindex_reference_links_1", "unique": true, "columns": ["tsn", "doc_id_prefix", "documentation_id"]}]}, "reviews": {"row_count": 0, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "review_start_date", "type": "date", "not_null": true, "default": null, "primary_key": true}, {"name": "review_end_date", "type": "date", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "review_reason", "type": "<PERSON><PERSON><PERSON>(25)", "not_null": true, "default": null, "primary_key": false}, {"name": "reviewer", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "review_comment", "type": "<PERSON><PERSON><PERSON>(255)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "reviews_reviews_index", "unique": false, "columns": ["tsn", "review_start_date"]}, {"name": "sqlite_autoindex_reviews_1", "unique": true, "columns": ["tsn", "review_start_date"]}]}, "strippedauthor": {"row_count": 210378, "columns": [{"name": "taxon_author_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "shortauthor", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "strippedauthor_taxon_author_id", "unique": false, "columns": ["taxon_author_id", "shortauthor"]}, {"name": "sqlite_autoindex_strippedauthor_1", "unique": true, "columns": ["taxon_author_id"]}]}, "synonym_links": {"row_count": 302348, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "tsn_accepted", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "synonym_links_synonym_links_index", "unique": false, "columns": ["tsn", "tsn_accepted"]}, {"name": "sqlite_autoindex_synonym_links_1", "unique": true, "columns": ["tsn", "tsn_accepted"]}]}, "taxon_authors_lkp": {"row_count": 210378, "columns": [{"name": "taxon_author_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "taxon_author", "type": "<PERSON><PERSON><PERSON>(100)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "kingdom_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": true}, {"name": "short_author", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "taxon_authors_lkp_taxon_authors_id_index", "unique": false, "columns": ["taxon_author_id", "taxon_author", "kingdom_id"]}, {"name": "sqlite_autoindex_taxon_authors_lkp_1", "unique": true, "columns": ["taxon_author_id", "kingdom_id"]}]}, "taxon_unit_types": {"row_count": 182, "columns": [{"name": "kingdom_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "rank_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": true}, {"name": "rank_name", "type": "char(15)", "not_null": true, "default": null, "primary_key": false}, {"name": "dir_parent_rank_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": false}, {"name": "req_parent_rank_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "taxon_unit_types_taxon_ut_index", "unique": false, "columns": ["kingdom_id", "rank_id"]}, {"name": "sqlite_autoindex_taxon_unit_types_1", "unique": true, "columns": ["kingdom_id", "rank_id"]}]}, "taxonomic_units": {"row_count": 970694, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "unit_ind1", "type": "char(1)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_name1", "type": "char(35)", "not_null": true, "default": null, "primary_key": false}, {"name": "unit_ind2", "type": "char(20)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_name2", "type": "<PERSON><PERSON><PERSON>(35)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_ind3", "type": "<PERSON><PERSON><PERSON>(20)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_name3", "type": "<PERSON><PERSON><PERSON>(35)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_ind4", "type": "<PERSON><PERSON><PERSON>(7)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unit_name4", "type": "<PERSON><PERSON><PERSON>(35)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "unnamed_taxon_ind", "type": "char(1)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "name_usage", "type": "<PERSON><PERSON><PERSON>(12)", "not_null": true, "default": null, "primary_key": false}, {"name": "unaccept_reason", "type": "<PERSON><PERSON><PERSON>(50)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "credibility_rtng", "type": "<PERSON><PERSON><PERSON>(40)", "not_null": true, "default": null, "primary_key": false}, {"name": "completeness_rtng", "type": "char(10)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "currency_rating", "type": "char(7)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "phylo_sort_seq", "type": "smallint(6)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "initial_time_stamp", "type": "datetime", "not_null": true, "default": null, "primary_key": false}, {"name": "parent_tsn", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "taxon_author_id", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "hybrid_author_id", "type": "int(11)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "kingdom_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": false}, {"name": "rank_id", "type": "smallint(6)", "not_null": true, "default": null, "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "uncertain_prnt_ind", "type": "char(3)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "n_usage", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "complete_name", "type": "tinytext", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "taxonomic_units_taxon_unit_index4", "unique": false, "columns": ["tsn", "taxon_author_id"]}, {"name": "taxonomic_units_taxon_unit_index3", "unique": false, "columns": ["kingdom_id", "rank_id"]}, {"name": "taxonomic_units_taxon_unit_index2", "unique": false, "columns": ["tsn", "unit_name1", "name_usage"]}, {"name": "taxonomic_units_taxon_unit_index1", "unique": false, "columns": ["tsn", "parent_tsn"]}, {"name": "sqlite_autoindex_taxonomic_units_1", "unique": true, "columns": ["tsn"]}]}, "tu_comments_links": {"row_count": 192211, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "comment_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "tu_comments_links_tu_comments_links_index", "unique": false, "columns": ["tsn", "comment_id"]}, {"name": "sqlite_autoindex_tu_comments_links_1", "unique": true, "columns": ["tsn", "comment_id"]}]}, "vern_ref_links": {"row_count": 89095, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "doc_id_prefix", "type": "char(3)", "not_null": true, "default": null, "primary_key": true}, {"name": "documentation_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "vern_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}], "indexes": [{"name": "vern_ref_links_vern_rl_index2", "unique": false, "columns": ["tsn", "vern_id"]}, {"name": "vern_ref_links_vern_rl_index1", "unique": false, "columns": ["tsn", "doc_id_prefix", "documentation_id"]}, {"name": "sqlite_autoindex_vern_ref_links_1", "unique": true, "columns": ["tsn", "doc_id_prefix", "documentation_id", "vern_id"]}]}, "vernaculars": {"row_count": 155767, "columns": [{"name": "tsn", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}, {"name": "vernacular_name", "type": "<PERSON><PERSON><PERSON>(80)", "not_null": true, "default": null, "primary_key": false}, {"name": "language", "type": "<PERSON><PERSON><PERSON>(15)", "not_null": true, "default": null, "primary_key": false}, {"name": "approved_ind", "type": "char(1)", "not_null": false, "default": "NULL", "primary_key": false}, {"name": "update_date", "type": "date", "not_null": true, "default": null, "primary_key": false}, {"name": "vern_id", "type": "int(11)", "not_null": true, "default": null, "primary_key": true}], "indexes": [{"name": "vernaculars_vernaculars_index2", "unique": false, "columns": ["tsn", "vern_id"]}, {"name": "vernaculars_vernaculars_index1", "unique": false, "columns": ["tsn", "vernacular_name", "language"]}, {"name": "sqlite_autoindex_vernaculars_1", "unique": true, "columns": ["tsn", "vern_id"]}]}}}, ".\\data\\unified_chroma\\chroma.sqlite3": {"path": ".\\data\\unified_chroma\\chroma.sqlite3", "size_bytes": 73023488, "size_mb": 69.64, "page_count": 17828, "page_size": 4096, "tables": {"migrations": {"row_count": 16, "columns": [{"name": "dir", "type": "TEXT", "not_null": true, "default": null, "primary_key": true}, {"name": "version", "type": "INTEGER", "not_null": true, "default": null, "primary_key": true}, {"name": "filename", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "sql", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "hash", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_migrations_1", "unique": true, "columns": ["dir", "version"]}]}, "acquire_write": {"row_count": 52, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "lock_status", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}], "indexes": []}, "collection_metadata": {"row_count": 0, "columns": [{"name": "collection_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "key", "type": "TEXT", "not_null": true, "default": null, "primary_key": true}, {"name": "str_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "int_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "float_value", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "bool_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_collection_metadata_1", "unique": true, "columns": ["collection_id", "key"]}]}, "segment_metadata": {"row_count": 0, "columns": [{"name": "segment_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "key", "type": "TEXT", "not_null": true, "default": null, "primary_key": true}, {"name": "str_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "int_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "float_value", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "bool_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_segment_metadata_1", "unique": true, "columns": ["segment_id", "key"]}]}, "tenants": {"row_count": 1, "columns": [{"name": "id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}], "indexes": [{"name": "sqlite_autoindex_tenants_1", "unique": true, "columns": ["id"]}]}, "databases": {"row_count": 1, "columns": [{"name": "id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "tenant_id", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_databases_2", "unique": true, "columns": ["tenant_id", "name"]}, {"name": "sqlite_autoindex_databases_1", "unique": true, "columns": ["id"]}]}, "collections": {"row_count": 1, "columns": [{"name": "id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "dimension", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "database_id", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "config_json_str", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_collections_2", "unique": true, "columns": ["name", "database_id"]}, {"name": "sqlite_autoindex_collections_1", "unique": true, "columns": ["id"]}]}, "maintenance_log": {"row_count": 0, "columns": [{"name": "id", "type": "INT", "not_null": false, "default": null, "primary_key": true}, {"name": "timestamp", "type": "INT", "not_null": true, "default": null, "primary_key": false}, {"name": "operation", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_maintenance_log_1", "unique": true, "columns": ["id"]}]}, "segments": {"row_count": 2, "columns": [{"name": "id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "type", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "scope", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "collection", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_segments_1", "unique": true, "columns": ["id"]}]}, "embeddings": {"row_count": 5581, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "segment_id", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "embedding_id", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "seq_id", "type": "BLOB", "not_null": true, "default": null, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": true, "default": "CURRENT_TIMESTAMP", "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_embeddings_1", "unique": true, "columns": ["segment_id", "embedding_id"]}]}, "embedding_metadata": {"row_count": 234972, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "key", "type": "TEXT", "not_null": true, "default": null, "primary_key": true}, {"name": "string_value", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "int_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}, {"name": "float_value", "type": "REAL", "not_null": false, "default": null, "primary_key": false}, {"name": "bool_value", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "embedding_metadata_string_value", "unique": false, "columns": ["key", "string_value"]}, {"name": "embedding_metadata_float_value", "unique": false, "columns": ["key", "float_value"]}, {"name": "embedding_metadata_int_value", "unique": false, "columns": ["key", "int_value"]}, {"name": "sqlite_autoindex_embedding_metadata_1", "unique": true, "columns": ["id", "key"]}]}, "max_seq_id": {"row_count": 2, "columns": [{"name": "segment_id", "type": "TEXT", "not_null": false, "default": null, "primary_key": true}, {"name": "seq_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_max_seq_id_1", "unique": true, "columns": ["segment_id"]}]}, "embedding_fulltext_search_data": {"row_count": 4400, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "block", "type": "BLOB", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "embedding_fulltext_search_idx": {"row_count": 3733, "columns": [{"name": "segid", "type": "", "not_null": true, "default": null, "primary_key": true}, {"name": "term", "type": "", "not_null": true, "default": null, "primary_key": true}, {"name": "pgno", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_embedding_fulltext_search_idx_1", "unique": true, "columns": ["segid", "term"]}]}, "embedding_fulltext_search_content": {"row_count": 5581, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "c0", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "embedding_fulltext_search_docsize": {"row_count": 5581, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "sz", "type": "BLOB", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "embedding_fulltext_search_config": {"row_count": 1, "columns": [{"name": "k", "type": "", "not_null": true, "default": null, "primary_key": true}, {"name": "v", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": [{"name": "sqlite_autoindex_embedding_fulltext_search_config_1", "unique": true, "columns": ["k"]}]}, "embeddings_queue": {"row_count": 821, "columns": [{"name": "seq_id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "created_at", "type": "TIMESTAMP", "not_null": true, "default": "CURRENT_TIMESTAMP", "primary_key": false}, {"name": "operation", "type": "INTEGER", "not_null": true, "default": null, "primary_key": false}, {"name": "topic", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "id", "type": "TEXT", "not_null": true, "default": null, "primary_key": false}, {"name": "vector", "type": "BLOB", "not_null": false, "default": null, "primary_key": false}, {"name": "encoding", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}, {"name": "metadata", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "embeddings_queue_config": {"row_count": 1, "columns": [{"name": "id", "type": "INTEGER", "not_null": false, "default": null, "primary_key": true}, {"name": "config_json_str", "type": "TEXT", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "sqlite_stat1": {"row_count": 21, "columns": [{"name": "tbl", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "idx", "type": "", "not_null": false, "default": null, "primary_key": false}, {"name": "stat", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}, "embedding_fulltext_search": {"row_count": 5581, "columns": [{"name": "string_value", "type": "", "not_null": false, "default": null, "primary_key": false}], "indexes": []}}}, ".\\instance\\chat_history.db": {"path": ".\\instance\\chat_history.db", "size_bytes": 0, "size_mb": 0.0, "page_count": 0, "page_size": 4096, "tables": {}}}, "summary": {"total_databases": 11, "total_size_mb": 953.65, "total_tables": 122, "database_types": {"sqlite": 11, "vector_databases": 1}}}