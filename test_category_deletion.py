#!/usr/bin/env python3
"""
Test script for category deletion prevention functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.helpers import check_category_has_related_data, delete_category_and_files

def test_category_validation():
    """Test the category validation functionality."""
    
    # Test with a category that likely has data
    test_category = "CANOPY"  # This is likely to have data
    
    print(f"Testing category validation for: {test_category}")
    print("=" * 50)
    
    # Check for related data
    data_check = check_category_has_related_data(test_category)
    
    print(f"Has data: {data_check['has_data']}")
    print(f"Total items: {data_check['total_items']}")
    print("\nData summary:")
    for key, value in data_check['data_summary'].items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 50)
    
    # Test deletion (should be prevented)
    print(f"Testing deletion for category: {test_category}")
    result = delete_category_and_files(test_category)
    
    print(f"Success: {result['success']}")
    print(f"Message: {result['message']}")
    
    if not result['success'] and result.get('has_related_data'):
        print("✅ Deletion correctly prevented due to related data")
    else:
        print("❌ Deletion prevention not working as expected")

if __name__ == "__main__":
    test_category_validation() 