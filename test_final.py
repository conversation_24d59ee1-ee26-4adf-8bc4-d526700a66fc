#!/usr/bin/env python3
"""
Final test script to check database connection and authentication.
"""

import os
import sys
import sqlite3

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_direct_connection():
    """Test direct database connection."""
    print("Testing direct database connection...")
    
    try:
        conn = sqlite3.connect("./user_management.db")
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT username, password_hash FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        
        if result:
            username, password_hash = result
            print(f"✅ Found user: {username}")
            print(f"   Password hash: {password_hash[:20]}...")
            
            # Test password verification
            import bcrypt
            test_password = "admin123"
            if bcrypt.checkpw(test_password.encode('utf-8'), password_hash.encode('utf-8')):
                print(f"✅ Password verification successful for {username}")
            else:
                print(f"❌ Password verification failed for {username}")
        else:
            print("❌ User 'admin' not found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Direct connection failed: {str(e)}")

def test_module_connection():
    """Test module database connection."""
    print(f"\nTesting module database connection...")
    
    try:
        # Import and check the module
        import app.utils.db_connection as db
        print(f"USER_DB_PATH from module: {db.USER_DB_PATH}")
        
        # Test query through module
        user_data = db.execute_query("SELECT username FROM users WHERE username = ?", ("admin",))
        
        if user_data:
            print(f"✅ Module query successful: {user_data[0]['username']}")
        else:
            print("❌ Module query failed - no results")
            
    except Exception as e:
        print(f"❌ Module connection failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_authentication():
    """Test authentication function."""
    print(f"\nTesting authentication function...")
    
    try:
        from app.services.user_management import authenticate_user
        
        # Test authentication
        user, error = authenticate_user("admin", "admin123")
        
        if user:
            print(f"✅ Authentication successful: {user.username}")
        else:
            print(f"❌ Authentication failed: {error}")
            
    except Exception as e:
        print(f"❌ Authentication test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_connection()
    test_module_connection()
    test_authentication()
