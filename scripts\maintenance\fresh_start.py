#!/usr/bin/env python3
"""
Fresh Start Script for ERDB System

This script performs a complete fresh start of all application databases
while preserving user management data. It includes comprehensive backup,
cleanup, reinitialization, and verification steps.

Usage:
    python scripts/maintenance/fresh_start.py [--confirm] [--skip-backup]
"""

import os
import sys
import shutil
import logging
import sqlite3
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fresh_start.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FreshStartManager:
    """Manages the fresh start process for ERDB system."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.resolve()
        self.backup_dir = self.project_root / 'backups'
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Database paths
        self.databases = {
            'user_management': self.project_root / 'user_management.db',
            'main': self.project_root / 'erdb_main.db',
            'chat_history': self.project_root / 'chat_history.db',
            'scraped_pages': self.project_root / 'scraped_pages.db',
            'content_db': self.project_root / 'data' / 'content_db.sqlite',
            'instance_chat': self.project_root / 'instance' / 'chat_history.db'
        }
        
        # Directories to clean
        self.directories_to_clean = [
            self.project_root / 'data' / 'unified_chroma',
            self.project_root / 'data' / 'temp',
            self.project_root / 'logs'
        ]
        
        # Preserve these directories
        self.preserve_directories = [
            self.project_root / 'data' / 'itisSqlite060625',  # External reference data
            self.project_root / 'backups',  # Backup directory
            self.project_root / 'venv',  # Virtual environment
            self.project_root / 'app',  # Application code
            self.project_root / 'scripts',  # Scripts
            self.project_root / 'config',  # Configuration
            self.project_root / 'docs',  # Documentation
            self.project_root / 'tests',  # Tests
            self.project_root / 'memory-bank'  # Memory bank
        ]
    
    def create_backup(self) -> bool:
        """Create comprehensive backup before fresh start."""
        logger.info("Creating comprehensive backup...")
        
        try:
            # Create backup directory
            backup_name = f"fresh_start_backup_{self.timestamp}"
            backup_path = self.backup_dir / backup_name
            backup_path.mkdir(exist_ok=True)
            
            # Backup databases
            db_backup_dir = backup_path / 'databases'
            db_backup_dir.mkdir(exist_ok=True)
            
            for name, db_path in self.databases.items():
                if db_path.exists():
                    backup_db_path = db_backup_dir / f"{name}.db"
                    shutil.copy2(db_path, backup_db_path)
                    logger.info(f"Backed up {name}: {db_path} -> {backup_db_path}")
            
            # Backup ChromaDB
            chroma_dir = self.project_root / 'data' / 'unified_chroma'
            if chroma_dir.exists():
                chroma_backup_dir = backup_path / 'unified_chroma'
                shutil.copytree(chroma_dir, chroma_backup_dir)
                logger.info(f"Backed up ChromaDB: {chroma_dir} -> {chroma_backup_dir}")
            
            # Backup configuration files
            config_files = [
                '.env',
                'config/settings.py',
                'config/alert_rules.json',
                'config/chunking_config.py'
            ]
            
            for config_file in config_files:
                config_path = self.project_root / config_file
                if config_path.exists():
                    config_backup_path = backup_path / config_file
                    config_backup_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(config_path, config_backup_path)
                    logger.info(f"Backed up config: {config_file}")
            
            # Create backup summary
            backup_summary = {
                'timestamp': self.timestamp,
                'backup_path': str(backup_path),
                'databases_backed_up': [name for name, path in self.databases.items() if path.exists()],
                'chroma_backed_up': chroma_dir.exists(),
                'config_files_backed_up': [f for f in config_files if (self.project_root / f).exists()]
            }
            
            with open(backup_path / 'backup_summary.json', 'w') as f:
                import json
                json.dump(backup_summary, f, indent=2)
            
            logger.info(f"Backup completed: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {str(e)}")
            return False
    
    def verify_user_management(self) -> bool:
        """Verify user management database integrity."""
        logger.info("Verifying user management database...")
        
        try:
            if not self.databases['user_management'].exists():
                logger.error("user_management.db not found!")
                return False
            
            # Test database connection and basic queries
            conn = sqlite3.connect(self.databases['user_management'])
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'permission_groups', 'user_sessions']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                logger.error(f"Missing required tables: {missing_tables}")
                return False
            
            # Check user count
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            logger.info(f"Found {user_count} users in user_management.db")
            
            # Check for admin users
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]
            logger.info(f"Found {admin_count} admin users")
            
            conn.close()
            
            if user_count == 0:
                logger.warning("No users found in user_management.db")
                return False
            
            logger.info("User management database verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"User management verification failed: {str(e)}")
            return False
    
    def cleanup_databases(self) -> bool:
        """Remove application databases (except user management)."""
        logger.info("Cleaning up application databases...")
        
        databases_to_remove = [
            'main', 'chat_history', 'scraped_pages', 'content_db', 'instance_chat'
        ]
        
        for db_name in databases_to_remove:
            db_path = self.databases[db_name]
            if db_path.exists():
                try:
                    db_path.unlink()
                    logger.info(f"Removed {db_name}: {db_path}")
                except Exception as e:
                    logger.error(f"Failed to remove {db_name}: {str(e)}")
                    return False
        
        return True
    
    def cleanup_directories(self) -> bool:
        """Clean up directories while preserving essential data."""
        logger.info("Cleaning up directories...")
        
        for dir_path in self.directories_to_clean:
            if dir_path.exists():
                try:
                    if dir_path.is_dir():
                        # Remove contents but preserve directory
                        for item in dir_path.iterdir():
                            if item.is_file() or item.is_symlink():
                                item.unlink()
                            elif item.is_dir():
                                shutil.rmtree(item)
                        logger.info(f"Cleaned directory: {dir_path}")
                    else:
                        dir_path.unlink()
                        logger.info(f"Removed file: {dir_path}")
                except Exception as e:
                    logger.error(f"Failed to clean {dir_path}: {str(e)}")
                    return False
        
        return True
    
    def reinitialize_databases(self) -> bool:
        """Reinitialize all application databases."""
        logger.info("Reinitializing application databases...")
        
        try:
            # Reinitialize main database
            logger.info("Reinitializing main database...")
            from app.models.schema import initialize_database as init_main_db
            if not init_main_db():
                logger.error("Failed to initialize main database")
                return False
            
            # Reinitialize chat and scraped pages databases
            logger.info("Reinitializing chat and scraped pages databases...")
            import app.utils.database as chat_db_utils
            chat_db_utils.init_db()
            
            # Create instance directory if it doesn't exist
            instance_dir = self.project_root / 'instance'
            instance_dir.mkdir(exist_ok=True)
            
            # Initialize instance chat database
            instance_chat_db = instance_dir / 'chat_history.db'
            if not instance_chat_db.exists():
                conn = sqlite3.connect(instance_chat_db)
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS chat_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        category TEXT,
                        question TEXT,
                        answer TEXT,
                        sources TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                conn.commit()
                conn.close()
                logger.info("Initialized instance chat database")
            
            logger.info("Database reinitialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database reinitialization failed: {str(e)}")
            return False
    
    def reinitialize_chroma(self) -> bool:
        """Reinitialize ChromaDB vector database."""
        logger.info("Reinitializing ChromaDB...")
        
        try:
            chroma_dir = self.project_root / 'data' / 'unified_chroma'
            chroma_dir.mkdir(parents=True, exist_ok=True)
            
            # Create a fresh ChromaDB instance
            import chromadb
            from chromadb.config import Settings
            
            # Initialize ChromaDB with fresh settings
            client = chromadb.PersistentClient(
                path=str(chroma_dir),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Create a default collection to ensure proper initialization
            try:
                collection = client.create_collection(
                    name="default",
                    metadata={"description": "Default collection for ERDB system"}
                )
                logger.info("Created default ChromaDB collection")
            except Exception as e:
                if "already exists" in str(e):
                    logger.info("Default ChromaDB collection already exists")
                else:
                    logger.warning(f"Could not create default collection: {str(e)}")
            
            logger.info("ChromaDB reinitialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"ChromaDB reinitialization failed: {str(e)}")
            return False
    
    def verify_fresh_start(self) -> bool:
        """Verify that the fresh start was successful."""
        logger.info("Verifying fresh start...")
        
        try:
            # Check if databases exist and are properly initialized
            required_databases = ['main', 'chat_history', 'scraped_pages']
            
            for db_name in required_databases:
                db_path = self.databases[db_name]
                if not db_path.exists():
                    logger.error(f"Required database missing: {db_name}")
                    return False
                
                # Test database connection
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    if not tables:
                        logger.error(f"Database {db_name} has no tables")
                        return False
                    
                    logger.info(f"Verified {db_name}: {len(tables)} tables found")
                    
                except Exception as e:
                    logger.error(f"Failed to verify {db_name}: {str(e)}")
                    return False
            
            # Check ChromaDB
            chroma_dir = self.project_root / 'data' / 'unified_chroma'
            if not chroma_dir.exists():
                logger.error("ChromaDB directory missing")
                return False
            
            # Check user management is still intact
            if not self.verify_user_management():
                logger.error("User management verification failed after fresh start")
                return False
            
            logger.info("Fresh start verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Fresh start verification failed: {str(e)}")
            return False
    
    def run_fresh_start(self, skip_backup: bool = False) -> bool:
        """Run the complete fresh start process."""
        logger.info("Starting fresh start process...")
        
        # Step 1: Create backup (unless skipped)
        if not skip_backup:
            if not self.create_backup():
                logger.error("Backup failed. Aborting fresh start.")
                return False
        
        # Step 2: Verify user management
        if not self.verify_user_management():
            logger.error("User management verification failed. Aborting fresh start.")
            return False
        
        # Step 3: Cleanup databases
        if not self.cleanup_databases():
            logger.error("Database cleanup failed. Aborting fresh start.")
            return False
        
        # Step 4: Cleanup directories
        if not self.cleanup_directories():
            logger.error("Directory cleanup failed. Aborting fresh start.")
            return False
        
        # Step 5: Reinitialize databases
        if not self.reinitialize_databases():
            logger.error("Database reinitialization failed. Aborting fresh start.")
            return False
        
        # Step 6: Reinitialize ChromaDB
        if not self.reinitialize_chroma():
            logger.error("ChromaDB reinitialization failed. Aborting fresh start.")
            return False
        
        # Step 7: Verify fresh start
        if not self.verify_fresh_start():
            logger.error("Fresh start verification failed.")
            return False
        
        logger.info("Fresh start completed successfully!")
        return True

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fresh Start for ERDB System')
    parser.add_argument('--confirm', action='store_true', 
                       help='Confirm fresh start without prompting')
    parser.add_argument('--skip-backup', action='store_true',
                       help='Skip backup creation (not recommended)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without executing')
    
    args = parser.parse_args()
    
    if not args.confirm and not args.dry_run:
        print("\n⚠️  WARNING: This will reset all application databases!")
        print("   - All PDF documents, chat history, and vector embeddings will be lost")
        print("   - User management data will be preserved")
        print("   - A backup will be created before proceeding")
        print("\nAre you sure you want to continue? (yes/no): ", end='')
        
        response = input().lower().strip()
        if response not in ['yes', 'y']:
            print("Fresh start cancelled.")
            return 1
    
    manager = FreshStartManager()
    
    if args.dry_run:
        print("\n=== DRY RUN - What would be done ===")
        print(f"Backup would be created: fresh_start_backup_{manager.timestamp}")
        print("Databases to be removed:")
        for name, path in manager.databases.items():
            if name != 'user_management' and path.exists():
                print(f"  - {name}: {path}")
        print("Directories to be cleaned:")
        for dir_path in manager.directories_to_clean:
            if dir_path.exists():
                print(f"  - {dir_path}")
        print("\nUser management database would be preserved:")
        print(f"  - user_management.db: {manager.databases['user_management']}")
        return 0
    
    success = manager.run_fresh_start(skip_backup=args.skip_backup)
    
    if success:
        print("\n✅ Fresh start completed successfully!")
        print("   - All application databases have been reset")
        print("   - User management data has been preserved")
        print("   - System is ready for fresh use")
        return 0
    else:
        print("\n❌ Fresh start failed!")
        print("   - Check the logs for details")
        print("   - You may need to restore from backup")
        return 1

if __name__ == '__main__':
    sys.exit(main())
