from flask import Blueprint

auth_bp = Blueprint('auth_bp', __name__)

"""
Authentication module for the user management system.

This module provides functions for user authentication, session management,
and related operations.
"""

import os
import datetime
import logging
import uuid
from typing import Tuple, Optional, Dict, Any
from functools import wraps
from flask import request, session, jsonify, flash, redirect, url_for
from flask_login import LoginManager, login_user, logout_user, current_user, AnonymousUserMixin

from app.utils import db_connection as db
from app.utils import security
from app.models.user import User

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SafeAnonymousUser(AnonymousUserMixin):
    """
    Custom anonymous user class that safely handles permission checks.
    """
    def has_dashboard_permission(self, function_name: str) -> bool:
        """
        Anonymous users have no permissions.

        Args:
            function_name: The dashboard function to check permissions for

        Returns:
            False (anonymous users have no permissions)
        """
        return False

    def has_permission(self, category: str, required_permission: str = 'read') -> bool:
        """
        Anonymous users have no permissions.

        Args:
            category: The category to check permissions for
            required_permission: The permission level required

        Returns:
            False (anonymous users have no permissions)
        """
        return False


def init_login_manager(app) -> LoginManager:
    """
    Initialize Flask-Login for the application.

    Args:
        app: The Flask application

    Returns:
        The LoginManager instance
    """
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'user.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    # Set custom anonymous user class
    login_manager.anonymous_user = SafeAnonymousUser

    @login_manager.user_loader
    def load_user_for_login(user_id):
        return get_user_by_id(user_id)

    return login_manager


def authenticate_user(username: str, password: str) -> Tuple[Optional[User], str]:
    """
    Authenticate a user with username and password.

    Args:
        username: The username to authenticate
        password: The password to verify

    Returns:
        A tuple of (user, error_message). If authentication fails, user will be None.
    """
    logger.info(f"Authentication attempt for username: {username}")
    try:
        # Get user by username
        user_data = db.execute_query("""
            SELECT user_id, username, password_hash, email, role, account_status,
                   failed_login_attempts, last_login, password_changed_at,
                   profile_picture, full_name, email_verified, group_id
            FROM users
            WHERE username = ?
        """, (username,))

        if not user_data:
            # User not found
            return None, "Invalid username or password"

        user_data = user_data[0]
        user_id = user_data['user_id']
        password_hash = user_data['password_hash']
        account_status = user_data['account_status']
        failed_attempts = user_data['failed_login_attempts'] or 0

        # Check account status
        if account_status == 'disabled':
            return None, "This account has been disabled. Please contact an administrator."

        if account_status == 'locked':
            return None, "This account is locked due to too many failed login attempts. Please contact an administrator."

        if account_status == 'pending':
            return None, "This account is pending approval. Please check your email or contact an administrator."

        # Check failed login attempts
        if failed_attempts >= security.MAX_LOGIN_ATTEMPTS:
            # Lock the account
            db.execute_update(
                "UPDATE users SET account_status = 'locked' WHERE user_id = ?",
                (user_id,)
            )

            # Log the account lockout
            log_user_activity(
                user_id=user_id,
                action_type="account_lockout",
                details="Account locked due to too many failed login attempts",
                status="warning",
                ip_address=request.remote_addr if request else None
            )

            return None, "Account locked due to too many failed login attempts. Please contact an administrator."

        # Verify password
        if not security.verify_password(password, password_hash):
            # Increment failed login attempts
            db.execute_update(
                "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE user_id = ?",
                (user_id,)
            )

            # Log the failed login attempt
            log_user_activity(
                user_id=user_id,
                action_type="login_failed",
                details="Failed login attempt",
                status="failure",
                ip_address=request.remote_addr if request else None
            )

            return None, "Invalid username or password"

        # Authentication successful - reset failed attempts and update last login
        now = datetime.datetime.now().isoformat()
        db.execute_update(
            "UPDATE users SET failed_login_attempts = 0, last_login = ? WHERE user_id = ?",
            (now, user_id)
        )

        # Create user object
        user = User(user_data)

        # Log the successful login
        log_user_activity(
            user_id=user_id,
            action_type="login",
            details="User logged in",
            status="success",
            ip_address=request.remote_addr if request else None
        )

        # Create or update session record
        session_id = create_user_session(user_id)
        if session_id:
            session['session_id'] = session_id

        logger.info(f"Authentication successful for username: {username}")
        return user, ""

    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        return None, f"An error occurred during authentication. Please try again later."


def create_user_session(user_id: int) -> Optional[str]:
    """
    Create a new user session record.

    Args:
        user_id: The ID of the user

    Returns:
        The session ID if successful, None otherwise
    """
    try:
        # Generate a unique session ID
        session_id = str(uuid.uuid4())

        # Get device fingerprint if available
        device_fingerprint = session.get('device_fingerprint')

        # Get IP address
        ip_address = request.remote_addr if request else None

        # Get user agent
        user_agent = request.headers.get('User-Agent') if request else None

        # Insert session record
        db.execute_insert("""
            INSERT INTO user_sessions (
                session_id, user_id, start_time, ip_address,
                user_agent, device_fingerprint
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            session_id,
            user_id,
            datetime.datetime.now().isoformat(),
            ip_address,
            user_agent,
            device_fingerprint
        ))

        return session_id
    except Exception as e:
        logger.error(f"Failed to create user session: {str(e)}")
        return None


def update_session_end(session_id: str) -> bool:
    """
    Update the end time of a user session.

    Args:
        session_id: The ID of the session to update

    Returns:
        True if successful, False otherwise
    """
    try:
        db.execute_update(
            "UPDATE user_sessions SET end_time = ? WHERE session_id = ?",
            (datetime.datetime.now().isoformat(), session_id)
        )
        return True
    except Exception as e:
        logger.error(f"Failed to update session end time: {str(e)}")
        return False


def login(user: User, remember: bool = False) -> bool:
    """
    Log in a user.

    Args:
        user: The user to log in
        remember: Whether to remember the user

    Returns:
        True if successful, False otherwise
    """
    try:
        login_user(user, remember=remember)
        return True
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return False


def logout() -> bool:
    """
    Log out the current user.

    Returns:
        True if successful, False otherwise
    """
    try:
        if current_user.is_authenticated:
            # Log the logout
            log_user_activity(
                user_id=current_user.user_id,
                action_type="logout",
                details="User logged out",
                status="success",
                ip_address=request.remote_addr
            )

            # Update session end time
            session_id = session.get('session_id')
            if session_id:
                update_session_end(session_id)

            # Logout user
            logout_user()

        # Clear session
        session.clear()

        return True
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        return False

def admin_required(f):
    """Decorator to require admin authentication for routes."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if request.endpoint == 'admin.admin_dashboard':
            return f(*args, **kwargs)

        if not current_user.is_authenticated:
            if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({"error": "Authentication required", "redirect": url_for('admin.admin_dashboard')}), 401
            else:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('admin.admin_dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def function_permission_required(function_name):
    """Decorator to require specific dashboard function permission."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            
            logger.info(f"Permission check for {function_name} - User: {current_user}, Authenticated: {current_user.is_authenticated}")
            
            if request.endpoint == 'admin.admin_dashboard':
                return f(*args, **kwargs)

            if not current_user.is_authenticated:
                logger.warning(f"Unauthenticated user attempting to access {function_name}")
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": "Authentication required", "redirect": url_for('admin.admin_dashboard')}), 401
                else:
                    flash('Please log in to access this page.', 'error')
                    return redirect(url_for('admin.admin_dashboard'))

            # Check if user has permission
            has_permission = current_user.has_dashboard_permission(function_name)
            is_admin = current_user.role == 'admin'
            
            logger.info(f"Permission check result - Function: {function_name}, Has Permission: {has_permission}, Is Admin: {is_admin}")
            
            if not has_permission and not is_admin:
                logger.warning(f"User {current_user.username} denied access to {function_name}")
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": f"Permission denied for {function_name.replace('_', ' ').title()}", "redirect": url_for('admin.admin_dashboard')}), 403
                else:
                    flash(f'You do not have permission to access {function_name.replace("_", " ").title()}.', 'error')
                    return redirect(url_for('admin.admin_dashboard'))

            logger.info(f"Permission granted for {function_name}")
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_user_by_id(user_id):
    """Get user by ID - defined here to avoid circular imports."""
    try:
        user_data = db.execute_query("""
            SELECT user_id, username, email, role, account_status,
                   failed_login_attempts, last_login, password_changed_at,
                   profile_picture, full_name, email_verified, group_id,
                   password_hash
            FROM users
            WHERE user_id = ?
        """, (user_id,))
        
        if user_data:
            return User(user_data[0])
        return None
    except Exception as e:
        logger.error(f"Error getting user by ID: {str(e)}")
        return None

def log_user_activity(user_id=None, action_type=None, details=None,
                     resource_type=None, resource_id=None, status="info",
                     ip_address=None):
    """Log user activity - defined here to avoid circular imports."""
    try:
        from datetime import datetime
        now = datetime.now().isoformat()
        
        db.execute_insert("""
            INSERT INTO user_activity_logs 
            (user_id, action_type, details, resource_type, resource_id, status, ip_address, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (user_id, action_type, details, resource_type, resource_id, status, ip_address, now))
        
        return True
    except Exception as e:
        logger.error(f"Error logging user activity: {str(e)}")
        return False
