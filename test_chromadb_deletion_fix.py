#!/usr/bin/env python3
"""
Test script to verify ChromaDB deletion fixes are working correctly.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.unified_vector_db import get_unified_vector_db
from app.utils.helpers import delete_vector_embeddings, vacuum_chromadb
import chromadb

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_vector_deletion():
    """Test the vector deletion functionality."""
    logger.info("=== Testing Vector Deletion ===")
    
    try:
        # Get unified vector database
        unified_db = get_unified_vector_db()
        client = unified_db._get_db()._client
        collection = client.get_collection(name="unified_collection")
        
        # Get initial stats
        initial_count = collection.count()
        logger.info(f"Initial document count: {initial_count}")
        
        # Test deletion with a non-existent file (should not fail)
        test_category = "TEST_CATEGORY"
        test_filename = "non_existent_file.pdf"
        
        logger.info(f"Testing deletion of non-existent file: {test_filename}")
        result = delete_vector_embeddings(test_category, test_filename)
        
        if result:
            logger.info("✅ Non-existent file deletion test passed")
        else:
            logger.warning("⚠️  Non-existent file deletion test failed (but this might be expected)")
        
        # Get final stats
        final_count = collection.count()
        logger.info(f"Final document count: {final_count}")
        
        if final_count == initial_count:
            logger.info("✅ Document count unchanged (expected for non-existent file)")
        else:
            logger.warning(f"⚠️  Document count changed: {initial_count} -> {final_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector deletion test failed: {str(e)}")
        return False

def test_vacuum_operation():
    """Test the VACUUM operation."""
    logger.info("\n=== Testing VACUUM Operation ===")
    
    try:
        # Get initial size
        db_path = "./data/unified_chroma/chroma.sqlite3"
        if os.path.exists(db_path):
            initial_size = os.path.getsize(db_path) / (1024 * 1024)
            logger.info(f"Initial ChromaDB size: {initial_size:.2f} MB")
        else:
            logger.warning("ChromaDB file not found")
            return False
        
        # Run VACUUM
        success, size_before, size_after, space_reclaimed = vacuum_chromadb()
        
        if success:
            logger.info(f"✅ VACUUM operation successful:")
            logger.info(f"  Before: {size_before:.2f} MB")
            logger.info(f"  After: {size_after:.2f} MB")
            logger.info(f"  Space reclaimed: {space_reclaimed:.2f} MB")
            return True
        else:
            logger.error("❌ VACUUM operation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ VACUUM test failed: {str(e)}")
        return False

def test_orphaned_vector_cleanup():
    """Test the orphaned vector cleanup functionality."""
    logger.info("\n=== Testing Orphaned Vector Cleanup ===")
    
    try:
        # Import the cleanup function
        from scripts.maintenance.cleanup_orphaned_vectors import cleanup_orphaned_vectors
        
        # Run cleanup in dry-run mode
        orphaned_count = cleanup_orphaned_vectors(dry_run=True)
        
        logger.info(f"Found {orphaned_count} orphaned vectors (dry run)")
        
        if orphaned_count >= 0:
            logger.info("✅ Orphaned vector cleanup test passed")
            return True
        else:
            logger.error("❌ Orphaned vector cleanup test failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Orphaned vector cleanup test failed: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("=== ChromaDB Deletion Fix Test Suite ===")
    
    tests = [
        ("Vector Deletion", test_vector_deletion),
        ("VACUUM Operation", test_vacuum_operation),
        ("Orphaned Vector Cleanup", test_orphaned_vector_cleanup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} test PASSED")
        else:
            logger.error(f"❌ {test_name} test FAILED")
    
    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 All tests passed! ChromaDB deletion fixes are working correctly.")
        return 0
    else:
        logger.warning(f"⚠️  {total - passed} test(s) failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        sys.exit(1)
