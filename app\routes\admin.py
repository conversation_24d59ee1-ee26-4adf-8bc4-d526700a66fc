from flask import Blueprint, render_template, request, flash, redirect, url_for, session, current_app, jsonify
from flask_login import current_user
from app.utils import helpers as utils
from app.services import user_service as um
from app.services.greeting_service import GreetingManager
from app.utils import database as db_utils
from functools import wraps
import logging
from app.routes.auth import admin_required, function_permission_required
from app.utils.health_monitor import get_health_monitor, check_system_health, get_system_metrics
import datetime

# Set up logging
logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard', methods=['GET'])
def admin_dashboard():
    categories = sorted(utils.list_categories())
    current_app.config['CATEGORIES'] = categories  # Store categories for use in user management
    
    # Check if AdminLTE theme is requested
    use_adminlte = request.args.get('theme') == 'adminlte'
    if use_adminlte:
        return render_template('admin_dashboard_adminlte.html')
    
    return render_template('admin_dashboard.html')

@admin_bp.route('/dashboard/adminlte', methods=['GET'])
def admin_dashboard_adminlte():
    """AdminLTE version of the admin dashboard."""
    categories = sorted(utils.list_categories())
    current_app.config['CATEGORIES'] = categories  # Store categories for use in user management
    return render_template('admin_dashboard_adminlte.html')

@admin_bp.route('/location_map')
def location_map():
    """Display the location map."""
    # Get all locations with their sources
    locations = db_utils.get_all_extracted_locations(include_sources=True)
    # Get location statistics
    statistics = db_utils.get_location_statistics()
    return render_template('location_map.html', 
                         locations=locations, 
                         statistics=statistics)

@admin_bp.route('/client-analytics')
def client_analytics():
    """View analytics for a specific client (legacy route, redirects to device analytics)."""
    # Get client name from request
    client_name = request.args.get('client')
    if not client_name:
        flash("Client name is required", "error")
        return redirect(url_for('admin.analytics_dashboard'))

    # Redirect to analytics dashboard since we're using device fingerprints now
    flash("Client analytics has been replaced with device-based analytics", "info")
    return redirect(url_for('admin.analytics_dashboard'))

@admin_bp.route('/device-analytics')
def device_analytics():
    """View analytics for a specific device."""
    # Get device fingerprint from request
    device_fingerprint = request.args.get('device_fingerprint')
    if not device_fingerprint:
        flash("Device fingerprint is required", "error")
        return redirect(url_for('admin.analytics_dashboard'))

    # Get date range filters from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get analytics data for this device
    analytics = db_utils.get_analytics(device_fingerprint=device_fingerprint, start_date=start_date, end_date=end_date)

    # Get summary statistics for this device
    summary = db_utils.get_analytics_summary(device_fingerprint=device_fingerprint)

    # Ensure device_fingerprint is always available in summary
    if 'device_fingerprint' not in summary:
        summary['device_fingerprint'] = device_fingerprint

    # Get the most recent client name for this device if available
    if analytics and not summary.get('client_name'):
        for entry in analytics:
            if entry.get('client_name'):
                summary['client_name'] = entry.get('client_name')
                break

    # Prepare data for activity chart
    activity_data = {"labels": [], "values": []}

    # Prepare data for category distribution chart
    category_data = {"labels": [], "values": []}

    # Prepare data for location map
    location_data = []


    # Process activity by date data if available
    if summary.get("activity_by_date"):
        for entry in summary["activity_by_date"]:
            activity_data["labels"].append(entry["date"])
            activity_data["values"].append(entry["count"])

    # Process category data
    if summary.get("top_categories"):
        for category in summary["top_categories"]:
            category_data["labels"].append(category["category"])
            category_data["values"].append(category["count"])

    # Process location data
    if analytics:
        # Create a set to track unique locations
        unique_locations = set()

        for entry in analytics:
            # Only include entries with valid location data
            if (entry.get('latitude') and entry.get('longitude') and
                entry.get('city') and entry.get('country')):

                # Create a unique key for this location
                location_key = f"{entry['latitude']}:{entry['longitude']}"

                # Only add if we haven't seen this location before
                if location_key not in unique_locations:
                    unique_locations.add(location_key)


                    location_data.append({
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'region': entry['region'],
                        'country': entry['country'],
                        'timestamp': entry.get('timestamp')
                    })


    return render_template('device_analytics.html',
                          analytics=analytics,
                          summary=summary,
                          activity_data=activity_data,
                          category_data=category_data,
                          location_data=location_data,
                          start_date=start_date,
                          end_date=end_date)

@admin_bp.route('/analytics')
@admin_required
def analytics_dashboard():
    """View AI analytics dashboard. Enhanced for Phase 2."""
    # Get date range filters from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get analytics data
    analytics = db_utils.get_analytics(start_date=start_date, end_date=end_date)

    # Get summary statistics
    summary = db_utils.get_analytics_summary()

    # Get greeting analytics for Phase 2
    greeting_manager = GreetingManager()
    greeting_analytics = greeting_manager.get_greeting_analytics(start_date=start_date, end_date=end_date)
    engagement_patterns = greeting_manager.get_time_based_engagement_patterns()

    # Add greeting analytics to summary
    summary['greeting_analytics'] = greeting_analytics
    summary['engagement_patterns'] = engagement_patterns

    # Import DEV_LOCATION from geo_utils to pass to template
    from app.services.geo_service import DEV_LOCATION

    # Get model performance analysis data if user has permission
    model_performance_data = None
    if current_user.has_dashboard_permission('model_performance_analysis'):
        model_performance_data = db_utils.get_model_performance_analysis(start_date=start_date, end_date=end_date)

    # Prepare data for processing time chart
    processing_time_data = {"labels": [], "values": []}

    # Prepare data for query volume chart
    query_volume_data = {"labels": [], "values": []}

    # Prepare data for location map using geoip_analytics data
    location_data = []

    if analytics:
        # Group analytics by date for query volume chart
        date_counts = {}
        for entry in analytics:
            # Extract date from timestamp (format: YYYY-MM-DD HH:MM:SS)
            if entry.get('timestamp'):
                date = entry['timestamp'].split(' ')[0]
                date_counts[date] = date_counts.get(date, 0) + 1

        # Sort dates and prepare chart data
        sorted_dates = sorted(date_counts.keys())
        query_volume_data["labels"] = sorted_dates
        query_volume_data["values"] = [date_counts[date] for date in sorted_dates]

        # Prepare processing time data (last 20 entries)
        recent_entries = analytics[:20]
        recent_entries.reverse()  # Show oldest to newest

        for entry in recent_entries:
            if entry.get('timestamp') and entry.get('processing_time') is not None:
                # Format timestamp for display
                timestamp = entry['timestamp'].split(' ')[1]  # Just show time
                processing_time_data["labels"].append(timestamp)
                processing_time_data["values"].append(float(entry['processing_time']))

    # Get geolocation data for the map from geoip_analytics table
    try:
        from app.services.geo_analytics import get_geoip_data

        # Get geoip data with the same date filters
        geoip_data = get_geoip_data(start_date=start_date, end_date=end_date)

        # Process geoip data for the map
        location_counts = {}  # Track visitor counts per location

        for entry in geoip_data:
            if (entry.get('latitude') and entry.get('longitude') and
                entry.get('city') and entry.get('country')):

                # Create a location key for grouping
                location_key = f"{entry['latitude']},{entry['longitude']}"

                if location_key not in location_counts:
                    location_counts[location_key] = {
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'region': entry.get('region', ''),
                        'country': entry['country'],
                        'count': 0,
                        'visitors': set(),
                        'client_names': set()
                    }

                # Count unique visitors (by device fingerprint)
                if entry.get('device_fingerprint'):
                    location_counts[location_key]['visitors'].add(entry['device_fingerprint'])

                # Collect client names
                if entry.get('client_name'):
                    location_counts[location_key]['client_names'].add(entry['client_name'])

                location_counts[location_key]['count'] += 1

        # Convert to location_data format expected by the template
        for location_info in location_counts.values():
            location_data.append({
                'latitude': location_info['latitude'],
                'longitude': location_info['longitude'],
                'city': location_info['city'],
                'region': location_info['region'],
                'country': location_info['country'],
                'count': len(location_info['visitors']),  # Unique visitor count
                'total_visits': location_info['count'],   # Total visit count
                'client_names': list(location_info['client_names'])[:5]  # Sample client names
            })

    except Exception as e:
        # Fallback to analytics data if geoip_analytics fails
        logger.error(f"Error retrieving geoip data: {str(e)}")

        # Create a set to track unique locations by device fingerprint
        unique_locations = set()

        for entry in analytics:
            # Only include entries with valid location data
            if (entry.get('latitude') and entry.get('longitude') and
                entry.get('device_fingerprint') and
                entry.get('city') and entry.get('country')):

                # Create a unique key for this location + device
                location_key = f"{entry['device_fingerprint']}:{entry['latitude']}:{entry['longitude']}"

                # Only add if we haven't seen this device at this location before
                if location_key not in unique_locations:
                    unique_locations.add(location_key)

                    location_data.append({
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'region': entry['region'],
                        'country': entry['country'],
                        'device_fingerprint': entry['device_fingerprint'],
                        'client_name': entry.get('client_name', 'Anonymous'),
                        'count': 1
                    })

    return render_template('analytics.html',
                          analytics=analytics,
                          summary=summary,
                          processing_time_data=processing_time_data,
                          query_volume_data=query_volume_data,
                          location_data=location_data,
                          start_date=start_date,
                          end_date=end_date,
                          dev_location=DEV_LOCATION,  # Pass DEV_LOCATION to template
                          model_performance_data=model_performance_data)

@admin_bp.route('/login', methods=['POST'])
def admin_login():
    """Handle admin login directly from the dashboard."""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember') == 'on'

        # Authenticate user
        user, error = um.authenticate_user(username, password)

        if user:
            # Check if password is expired
            if user.password_expired:
                # Store user ID in session for password change
                session['password_expired_user_id'] = user.user_id
                flash('Your password has expired. Please create a new password.', 'warning')
                return redirect(url_for('user.change_expired_password'))

            # Log in user
            um.login_user(user, remember=remember)


            # Store device fingerprint if available
            device_fingerprint = request.form.get('device_fingerprint')
            if device_fingerprint:
                session['device_fingerprint'] = device_fingerprint

            flash(f'Welcome, {user.username}!', 'success')
            return redirect(url_for('admin.admin_dashboard'))
        else:
            flash(error, 'error')
            return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/health')
@admin_required
@function_permission_required('system_monitoring')
def health_dashboard():
    """Enhanced system health monitoring dashboard with performance monitoring"""
    try:
        # Get health monitor data
        monitor = get_health_monitor()
        health_status = monitor.check_health_status()
        system_metrics = monitor.get_system_metrics()
        db_metrics = monitor.get_database_metrics()
        metrics_history = monitor.get_metrics_history(hours=24)

        # Get performance monitoring data
        performance_data = {}
        try:
            from app.utils.performance_monitor import get_performance_monitor
            perf_monitor = get_performance_monitor()
            if perf_monitor:
                performance_data['recent_metrics'] = perf_monitor.get_recent_metrics(hours=1)
                performance_data['function_stats'] = perf_monitor.get_function_stats()
                performance_data['bottlenecks'] = perf_monitor.get_bottlenecks()
        except ImportError:
            performance_data = {}

        # Get database optimization data
        optimization_data = {}
        try:
            from app.utils.database_optimizer import get_database_optimizer
            db_optimizer = get_database_optimizer()
            if db_optimizer:
                optimization_data['table_stats'] = db_optimizer.get_table_statistics()
                optimization_data['indexes'] = db_optimizer.analyze_indexes()
                optimization_data['suggestions'] = db_optimizer.suggest_indexes()
        except ImportError:
            optimization_data = {}

        # Get batch processing status
        batch_data = {}
        try:
            from app.utils.batch_processor import get_batch_processor
            batch_processor = get_batch_processor()
            if batch_processor:
                batch_data['resource_stats'] = batch_processor.get_resource_stats()
        except ImportError:
            batch_data = {}

        # Get ChromaDB performance data
        chroma_data = {}
        try:
            from app.utils.chroma_performance import get_chroma_monitor
            chroma_monitor = get_chroma_monitor()
            if chroma_monitor:
                chroma_data['collection_stats'] = chroma_monitor.get_collection_stats()
                chroma_data['slow_operations'] = chroma_monitor.get_slow_operations(threshold_seconds=1.0)
        except ImportError:
            chroma_data = {}

        return render_template('admin_health_enhanced.html',
                             health_status=health_status,
                             system_metrics=system_metrics,
                             db_metrics=db_metrics,
                             metrics_history=metrics_history,
                             performance_data=performance_data,
                             optimization_data=optimization_data,
                             batch_data=batch_data,
                             chroma_data=chroma_data)
    
    except Exception as e:
        logger.error(f"Error loading health dashboard: {str(e)}")
        flash(f"Error loading health dashboard: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/health/api/status')
@admin_required
@function_permission_required('system_monitoring')
def api_health_status():
    """API endpoint for health status"""
    try:
        health_status = check_system_health()
        return jsonify({
            'success': True,
            'health_status': {
                'status': health_status.status,
                'score': health_status.score,
                'issues': health_status.issues,
                'recommendations': health_status.recommendations,
                'last_check': health_status.last_check
            }
        })
    
    except Exception as e:
        logger.error(f"Error getting health status: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/api/metrics')
@admin_required
@function_permission_required('system_monitoring')
def api_health_metrics():
    """API endpoint for system metrics"""
    try:
        monitor = get_health_monitor()
        system_metrics = monitor.get_system_metrics()
        db_metrics = monitor.get_database_metrics()
        
        return jsonify({
            'success': True,
            'system_metrics': {
                'cpu_percent': system_metrics.cpu_percent,
                'memory_percent': system_metrics.memory_percent,
                'memory_available_mb': system_metrics.memory_available_mb,
                'disk_usage_percent': system_metrics.disk_usage_percent,
                'disk_free_gb': system_metrics.disk_free_gb,
                'active_connections': system_metrics.active_connections,
                'database_size_mb': system_metrics.database_size_mb,
                'uptime_seconds': system_metrics.uptime_seconds
            },
            'database_metrics': [
                {
                    'database_name': db.name,
                    'size_mb': db.size_mb,
                    'page_count': db.page_count,
                    'cache_hit_ratio': db.cache_hit_ratio,
                    'active_connections': db.active_connections,
                    'last_vacuum': db.last_vacuum,
                    'integrity_check': db.integrity_check
                }
                for db in db_metrics
            ]
        })
    
    except Exception as e:
        logger.error(f"Error getting system metrics: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/export')
@admin_required
@function_permission_required('system_monitoring')
def export_health_metrics():
    """Export health metrics to JSON file"""
    try:
        monitor = get_health_monitor()
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        export_path = f"./logs/health_metrics_{timestamp}.json"
        
        success = monitor.export_metrics(export_path)
        
        if success:
            flash(f"Health metrics exported to {export_path}", "success")
        else:
            flash("Failed to export health metrics", "error")
        
        return redirect(url_for('admin.health_dashboard'))
    
    except Exception as e:
        logger.error(f"Error exporting health metrics: {str(e)}")
        flash(f"Error exporting health metrics: {str(e)}", "error")
        return redirect(url_for('admin.health_dashboard'))

@admin_bp.route('/health/api/performance-metrics')
@admin_required
@function_permission_required('system_monitoring')
def api_performance_metrics():
    """API endpoint for performance metrics"""
    try:
        from app.utils.performance_monitor import get_performance_monitor

        perf_monitor = get_performance_monitor()
        if not perf_monitor:
            return jsonify({'success': False, 'error': 'Performance monitor not available'}), 503

        hours = request.args.get('hours', 1, type=int)
        recent_metrics = perf_monitor.get_recent_metrics(hours)
        function_stats = perf_monitor.get_function_stats()
        bottlenecks = perf_monitor.get_bottlenecks()

        # Convert metrics to JSON-serializable format
        metrics_data = []
        for metric in recent_metrics:
            metrics_data.append({
                'function_name': metric.function_name,
                'module_name': metric.module_name,
                'execution_time': metric.execution_time,
                'memory_before_mb': metric.memory_before_mb,
                'memory_after_mb': metric.memory_after_mb,
                'memory_peak_mb': metric.memory_peak_mb,
                'cpu_percent': metric.cpu_percent,
                'timestamp': metric.timestamp,
                'result_size': metric.result_size,
                'error': metric.error
            })

        bottlenecks_data = []
        for bottleneck in bottlenecks:
            bottlenecks_data.append({
                'function_name': bottleneck.function_name,
                'severity': bottleneck.severity,
                'message': bottleneck.message,
                'execution_time': bottleneck.execution_time,
                'memory_usage_mb': bottleneck.memory_usage_mb,
                'timestamp': bottleneck.timestamp,
                'recommendations': bottleneck.recommendations
            })

        return jsonify({
            'success': True,
            'data': {
                'recent_metrics': metrics_data,
                'function_statistics': function_stats,
                'bottlenecks': bottlenecks_data,
                'metrics_count': len(metrics_data)
            }
        })
    except ImportError:
        return jsonify({'success': False, 'error': 'Performance monitoring not available'}), 503
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/health/api/database-optimization')
@admin_required
@function_permission_required('system_monitoring')
def api_database_optimization():
    """API endpoint for database optimization data"""
    try:
        from app.utils.database_optimizer import get_database_optimizer

        db_optimizer = get_database_optimizer()
        if not db_optimizer:
            return jsonify({'success': False, 'error': 'Database optimizer not available'}), 503

        table_stats = db_optimizer.get_table_statistics()
        indexes = db_optimizer.analyze_indexes()
        suggestions = db_optimizer.suggest_indexes()

        table_stats_data = []
        for stat in table_stats:
            table_stats_data.append({
                'table_name': stat.table_name,
                'row_count': stat.row_count,
                'size_kb': stat.size_kb,
                'index_count': stat.index_count,
                'last_analyzed': stat.last_analyzed
            })

        indexes_data = []
        for index in indexes:
            indexes_data.append({
                'name': index.name,
                'table': index.table,
                'columns': index.columns,
                'unique': index.unique,
                'partial': index.partial,
                'size_kb': index.size_kb
            })

        return jsonify({
            'success': True,
            'data': {
                'table_statistics': table_stats_data,
                'indexes': indexes_data,
                'suggestions': suggestions
            }
        })
    except ImportError:
        return jsonify({'success': False, 'error': 'Database optimizer not available'}), 503
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/health/api/optimize-database', methods=['POST'])
@admin_required
@function_permission_required('system_monitoring')
def api_optimize_database():
    """API endpoint to trigger database optimization"""
    try:
        from app.utils.database_optimizer import get_database_optimizer

        db_optimizer = get_database_optimizer()
        if not db_optimizer:
            return jsonify({'success': False, 'error': 'Database optimizer not available'}), 503

        # Create recommended indexes
        index_results = db_optimizer.create_recommended_indexes()

        # Get updated suggestions
        suggestions = db_optimizer.suggest_indexes()

        return jsonify({
            'success': True,
            'data': {
                'index_results': index_results,
                'suggestions': suggestions,
                'message': f"Created {len([r for r in index_results if r['status'] == 'created'])} new indexes"
            }
        })
    except ImportError:
        return jsonify({'success': False, 'error': 'Database optimizer not available'}), 503
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/health/api/vacuum-databases', methods=['POST'])
@admin_required
@function_permission_required('system_monitoring')
def api_vacuum_databases():
    """API endpoint to trigger database vacuum operations"""
    try:
        from app.utils.health_monitor import get_health_monitor

        monitor = get_health_monitor()
        if not monitor:
            return jsonify({'success': False, 'error': 'Health monitor not available'}), 503

        # Perform vacuum operation
        vacuum_results = monitor.vacuum_all_databases()

        # Prepare response message
        if vacuum_results['success']:
            if vacuum_results['failed_vacuums'] == 0:
                message = f"Successfully vacuumed {vacuum_results['successful_vacuums']} database(s). "
                message += f"Total space reclaimed: {vacuum_results['total_space_reclaimed_mb']:.2f} MB"
            else:
                message = f"Partially successful: {vacuum_results['successful_vacuums']} succeeded, "
                message += f"{vacuum_results['failed_vacuums']} failed. "
                message += f"Space reclaimed: {vacuum_results['total_space_reclaimed_mb']:.2f} MB"
        else:
            message = f"Vacuum operation failed. {vacuum_results['failed_vacuums']} database(s) could not be vacuumed."

        return jsonify({
            'success': vacuum_results['success'],
            'data': {
                'message': message,
                'total_databases': vacuum_results['total_databases'],
                'successful_vacuums': vacuum_results['successful_vacuums'],
                'failed_vacuums': vacuum_results['failed_vacuums'],
                'total_space_reclaimed_mb': vacuum_results['total_space_reclaimed_mb'],
                'details': vacuum_results['details'],
                'errors': vacuum_results['errors']
            }
        })
    except ImportError:
        return jsonify({'success': False, 'error': 'Health monitor not available'}), 503
    except Exception as e:
        logger.error(f"Error during vacuum operation: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/health/api/batch-status')
@admin_required
@function_permission_required('system_monitoring')
def api_batch_status():
    """API endpoint for batch processing status"""
    try:
        from app.utils.batch_processor import get_batch_processor

        batch_processor = get_batch_processor()
        if not batch_processor:
            return jsonify({'success': False, 'error': 'Batch processor not available'}), 503

        resource_stats = batch_processor.get_resource_stats()

        return jsonify({
            'success': True,
            'data': {
                'resource_stats': resource_stats
            }
        })
    except ImportError:
        return jsonify({'success': False, 'error': 'Batch processor not available'}), 503
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/health/api/database-cleanup', methods=['POST'])
@admin_required
@function_permission_required('system_monitoring')
def api_database_cleanup():
    """API endpoint for comprehensive database cleanup"""
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Perform comprehensive cleanup
        cleanup_results = monitor.cleanup_database_space()
        
        return jsonify({
            'success': cleanup_results['success'],
            'message': f"Database cleanup completed. Reclaimed {cleanup_results['total_space_reclaimed_mb']:.2f} MB total",
            'results': cleanup_results
        })
    
    except Exception as e:
        logger.error(f"Error during database cleanup: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/api/analyze-sizes')
@admin_required
@function_permission_required('system_monitoring')
def api_analyze_sizes():
    """API endpoint for database size analysis"""
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Analyze database sizes
        size_analysis = monitor.analyze_database_sizes()
        
        # Get baseline comparison
        baselines = monitor.get_baseline_sizes()
        
        return jsonify({
            'success': True,
            'size_analysis': size_analysis,
            'baselines': baselines
        })
    
    except Exception as e:
        logger.error(f"Error analyzing database sizes: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/api/optimize-all-databases', methods=['POST'])
@admin_required
@function_permission_required('system_monitoring')
def api_optimize_all_databases():
    """API endpoint for comprehensive database optimization"""
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Perform comprehensive optimization
        optimization_results = monitor.optimize_all_databases()
        
        return jsonify({
            'success': optimization_results['success'],
            'message': f"Database optimization completed. Reclaimed {optimization_results['total_space_reclaimed_mb']:.2f} MB total",
            'results': optimization_results
        })
    
    except Exception as e:
        logger.error(f"Error during database optimization: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/api/establish-baselines', methods=['POST'])
@admin_required
@function_permission_required('system_monitoring')
def api_establish_baselines():
    """API endpoint for establishing database baselines"""
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Establish new baselines
        baselines = monitor._create_baselines()
        
        return jsonify({
            'success': True,
            'message': f"Database baselines established at {baselines['established_at']}",
            'baselines': baselines
        })
    
    except Exception as e:
        logger.error(f"Error establishing baselines: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@admin_bp.route('/health/database-cleanup')
@admin_required
@function_permission_required('system_monitoring')
def database_cleanup_dashboard():
    """Database cleanup and size management dashboard"""
    try:
        from app.utils.health_monitor import get_health_monitor
        monitor = get_health_monitor()
        
        # Get current analysis
        size_analysis = monitor.analyze_database_sizes()
        baselines = monitor.get_baseline_sizes()
        
        # Calculate changes from baseline
        changes = {}
        for name, db_info in size_analysis['databases'].items():
            if name in baselines.get('databases', {}):
                baseline_size = baselines['databases'][name].get('baseline_size_mb', 0)
                current_size = db_info['size_mb']
                change_mb = current_size - baseline_size
                change_percentage = (change_mb / baseline_size * 100) if baseline_size > 0 else 0
                
                changes[name] = {
                    'baseline_size_mb': baseline_size,
                    'current_size_mb': current_size,
                    'change_mb': change_mb,
                    'change_percentage': change_percentage
                }
        
        return render_template('admin_database_cleanup.html',
                             size_analysis=size_analysis,
                             baselines=baselines,
                             changes=changes)
    
    except Exception as e:
        logger.error(f"Error loading database cleanup dashboard: {str(e)}")
        flash(f"Error loading database cleanup dashboard: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))


