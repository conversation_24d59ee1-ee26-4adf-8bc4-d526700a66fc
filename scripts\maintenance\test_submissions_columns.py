#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify submissions data breakdown into columns
"""

import os
import sys
import sqlite3
import json

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'app'))

# Database path
DB_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'erdb_main.db')

def test_submissions_data_structure():
    """Test the structure of submissions data to ensure it can be broken down into columns."""
    
    print("🔍 TESTING SUBMISSIONS DATA STRUCTURE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get all submissions
        cursor.execute("""
            SELECT fs.id, fs.submission_data, f.name as form_name, pd.original_filename as pdf_filename
            FROM form_submissions fs
            JOIN forms f ON fs.form_id = f.id
            JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
            ORDER BY fs.submitted_at DESC
            LIMIT 5
        """)
        
        submissions = cursor.fetchall()
        
        if not submissions:
            print("❌ No submissions found in database")
            return False
            
        print(f"✅ Found {len(submissions)} submissions to test:")
        print()
        
        for sub_id, submission_data, form_name, pdf_filename in submissions:
            print(f"📄 Submission ID: {sub_id}")
            print(f"   Form: {form_name}")
            print(f"   PDF: {pdf_filename}")
            
            # Parse the JSON data
            try:
                data = json.loads(submission_data)
                print(f"   📊 Parsed data structure:")
                
                # Check for expected fields
                expected_fields = ['email', 'full_name', 'organization', 'purpose']
                for field in expected_fields:
                    value = data.get(field, '')
                    if value:
                        print(f"      ✅ {field}: {value}")
                    else:
                        print(f"      ⚠️  {field}: (empty)")
                
                # Check for unexpected fields
                unexpected_fields = [k for k in data.keys() if k not in expected_fields]
                if unexpected_fields:
                    print(f"      🔍 Unexpected fields: {unexpected_fields}")
                
                print()
                
            except json.JSONDecodeError as e:
                print(f"   ❌ Error parsing JSON: {e}")
                print()
        
        print("✅ All submissions data structure tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing submissions data structure: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_column_extraction():
    """Test extracting individual columns from submission data."""
    
    print("\n🔍 TESTING COLUMN EXTRACTION")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get a sample submission
        cursor.execute("""
            SELECT submission_data FROM form_submissions LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ No submissions found for testing")
            return False
            
        submission_data = json.loads(result[0])
        
        print("📊 Testing column extraction from sample submission:")
        print()
        
        # Test each field extraction
        fields = {
            'email': submission_data.get('email', ''),
            'full_name': submission_data.get('full_name', ''),
            'organization': submission_data.get('organization', ''),
            'purpose': submission_data.get('purpose', '')
        }
        
        for field_name, field_value in fields.items():
            if field_value:
                print(f"   ✅ {field_name}: {field_value}")
            else:
                print(f"   ⚠️  {field_name}: (empty)")
        
        print()
        print("✅ Column extraction test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing column extraction: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 Starting submissions columns test...")
    print()
    
    success1 = test_submissions_data_structure()
    success2 = test_column_extraction()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All tests passed! Submissions data can be successfully broken down into columns.")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    print("\n📝 Next steps:")
    print("   1. The template has been updated to show separate columns")
    print("   2. Test the /admin/submissions page in your browser")
    print("   3. Verify that email, full name, organization, and purpose are displayed in separate columns") 